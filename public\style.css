@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap");

body {
    margin: 0;
    padding: 0;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    background: linear-gradient(rgba(20, 71, 187, 0.3), rgba(255, 255, 255, 0.2)), url("BG.png") no-repeat center center fixed;
    background-size: cover;
    font-family: 'Montserrat', sans-serif;
    color: #333333;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
}

#header {
    display: none;
    font-family: 'Montserrat', sans-serif;
    font-size: 15px;
}

.header-texts {
    display: flex;
    justify-content: space-around;
    width: 100%;
    align-items: center;
    font-family: 'Montserrat', sans-serif;
    font-size: 15px;
}

@media (max-width: 768px) {
    #header {
        padding: 10px;
        font-size: 12px;
        flex-direction: column;
        height: auto;
        justify-content: center;
    }

    .header-texts, #header h2 {
        flex-direction: column;
        text-align: center;
    }

    #header h2 {
        margin: 5px 0;
        font-size: 12px;
    }
}

#failure-container h2 {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 20px;
    line-height: 1.4;
}

#failure-container h2:first-of-type {
    font-size: 18px;
    font-weight: 600;
    color: #1547BB;
}

#failure-container .blue-text {
    color: #1547BB;
}

#score-container {
    width: 90%;
    max-width: 550px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: box-shadow 0.3s ease;
    font-weight: 400;
    font-size: 15px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1001;
    max-height: 80vh;
    overflow-y: auto;
    font-family: 'Montserrat', sans-serif;
}

@media (max-width: 768px) {
    #quiz-container,
    #score-container {
        padding: 20px;
        font-size: 12px;
    }
}

.option-btn,
#highscore-btn,
#next-btn {
    background-color: #1547bb;
    color: #fff;
    padding: 10px 18px;
    margin: 10px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 12px;
}

.option-btn:hover,
#highscore-btn:hover,
#next-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

#success-container h2 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

#success-container .centered-image {
    max-width: 100px;
    margin-bottom: 30px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    transition: transform 0.3s ease;
}

#success-container .centered-image:hover {
    transform: scale(1.1);
}

#final-success-container {
    width: 90%;
    max-width: 600px;
    background: #ffffff60;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: box-shadow 0.3s ease;
    margin-top: 80px;
    font-weight: 400;
    font-size: 15px;
    position: relative;
    backdrop-filter: blur(16px);
    z-index: 100;
    font-family: "Montserrat", sans-serif;
}

@media (max-width: 768px) {
    #final-success-container {
        padding: 20px;
        font-size: 12px;
    }
}

#final-success-container h2 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
    text-transform: uppercase;
    line-height: 1.4;
}

#final-success-container .centered-image {
    max-width: 200px;
    margin-bottom: 30px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    transition: transform 0.3s ease;
}

#final-success-container .centered-image:hover {
    transform: scale(1.1);
}

#result-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 30px;
}

#start-page {
    width: 90%;
    max-width: 500px;
    padding: 24px;
    border-radius: 12px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    animation: fadeIn 1.2s ease-out forwards;
}

@media (max-width: 768px) {
    #start-page {
        padding: 20px;
    }
}

#start-page h1 {
    font-size: 22px;
    font-weight: 400;
    color: #1447BA;
    text-align: center;
    margin-bottom: 10px;
    line-height: 1.3;
}

#start-page h2 {
    font-size: 22px;
    font-weight: 700;
    color: #1447BA;
    text-align: center;
    margin-top: 10px;
    line-height: 1.3;
}

#start-page p {
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    text-align: center;
    margin-bottom: 34px;
    line-height: 1.5;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    opacity: 0;
    animation: contentFadeIn 1.2s ease-out forwards;
    animation-delay: 0.6s;
}

#start-page img {
    width: 200px;
    height: auto;
    margin: 1rem auto;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    opacity: 0;
    animation: contentFadeIn 1.2s ease-out forwards;
    animation-delay: 0.3s;
}

#start-btn {
    background-color: #1547bb;
    color: #fffffff6;
    padding: 14px 28px;
    border: none;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    opacity: 0;
    animation: contentFadeIn 1.2s ease-out forwards;
    animation-delay: 0.9s;
}

@media (max-width: 768px) {
    #start-btn {
        padding: 12px 24px;
        font-size: 13px;
    }
}

#start-btn:hover {
    background-color: #121c41;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

#progress-bar {
    height: 20px;
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
}

#progress-bar-fill {
    display: block;
    height: 100%;
    width: 0;
    background: #1547bb;
    transition: width 0.5s ease-in-out;
}

.skip-btn {
    background-color: #1547bb;
    color: #fff;
    padding: 10px 18px;
    margin: 10px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 14px;
}

.skip-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.option-btn {
    background-color: #1547bb;
    color: #fff;
    padding: 10px 18px;
    margin: 5px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 12px;
}

.option-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.correct {
    background: #28a745 !important; /* Changed from #7ae582 to a more visible green */
}

.wrong {
    background: #e74c3c !important;
}

#next-btn {
    background-color: #7ae582;
    color: #fff;
    padding: 10px 18px;
    margin: 5px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 12px;
}

#next-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

#success-container,
#failure-container,
#confirmation-container,
#result-container {
    width: 90%;
    max-width: 800px;
    padding: 20px;
    border-radius: 20px;
    text-align: center;
    margin: 40px auto;
    font-family: 'Montserrat', sans-serif;
    color: #333333;
    display: none;
    font-size: 15px;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    #success-container,
    #failure-container,
    #confirmation-container,
    #result-container {
        width: 95%;
        padding: 15px;
        margin: 20px auto;
        font-size: 13px;
        max-height: 90vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
}

#success-container h2#success-heading,
#result-container h2#result-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    line-height: 1.4;
    text-transform: uppercase;
    color: #1547BB;
}

#success-container h2#success-heading1,
#result-container h3#remarks {
    font-size: 15px;
    font-weight: 700;
    margin-bottom: 20px;
    text-transform: uppercase;
    color: #1547BB;
}

#failure-container h2#recommendation-text,
#result-container p {
    font-size: 15px;
    font-weight: 400;
    color: #1447BA;
    margin-bottom: 20px;
    line-height: 1.6;
}

@media (max-width: 768px) {
    #success-container h2#success-heading,
    #result-container h2#result-text {
        font-size: 15px;
    }

    #success-container h2#success-heading1,
    #result-container h3#remarks {
        font-size: 13px;
    }

    #failure-container h2#recommendation-text,
    #result-container p {
        font-size: 13px;
        line-height: 1.5;
    }
}

#result-container .book-btn,
.book-btn {
    font-size: 15px;
    font-weight: bold;
    padding: 12px 24px;
    margin-top: 20px;
    background-color: #1547bb;
    color: #ffffff;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: inline-block;
}

@media (max-width: 768px) {
    #result-container .book-btn,
    .book-btn {
        font-size: 13px;
        padding: 10px 20px;
    }
}

#result-container .book-btn:hover,
.book-btn:hover {
    background-color: #121c41;
}

#result-container .book-text,
.book-text {
    font-size: 15px;
    font-weight: 700;
    color: #1447BA;
    margin-top: 15px;
    display: block;
}

@media (max-width: 768px) {
    #result-container .book-text,
    .book-text {
        font-size: 13px;
    }
}

#result-container .book-text .regular-text,
.book-text .regular-text {
    font-size: 15px;
    font-weight: 400;
    text-decoration: none;
    color: #1447BA;
    display: block;
    margin-top: 10px;
}

@media (max-width: 768px) {
    #result-container .book-text .regular-text,
    .book-text .regular-text {
        font-size: 13px;
    }
}

#result-container .book-text a,
.book-text a,
.book-link {
    text-decoration: underline;
    color: inherit;
    transition: color 0.3s ease;
}

#result-container .book-text a:hover,
.book-text a:hover,
.book-link:hover {
    color: #1547bb;
}

#confirmation-container {
    width: 80%;
    max-width: 650px;
}

@media (max-width: 768px) {
    #confirmation-container {
        width: 95%;
        padding: 15px;
    }
}

#confirmation-container h2 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
    line-height: 1.4;
    text-transform: uppercase;
    color: #1547BB;
}

#confirmation-container h3 {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #1547BB;
}

@media (max-width: 768px) {
    #confirmation-container h2 {
        font-size: 15px;
    }

    #confirmation-container h3 {
        font-size: 13px;
    }
}

#logo-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

#logo-container img {
    width: 200px;
    height: auto;
    max-width: 80%;
    max-height: 80%;
}

@media (max-width: 768px) {
    #logo-container {
        bottom: 10px;
        right: 10px;
    }

    #logo-container img {
        width: 120px;
        max-width: 50%;
        max-height: 50%;
    }
}


#pathway-container {
    display: none;
    max-width: 800px;
    width: 90%;
    padding: 20px;
    border-radius: 20px;
    text-align: center;
    margin: 40px auto;
    font-family: 'Montserrat', sans-serif;
    color: #1547BB;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    position: relative;
}

#pathway-container .close-btn {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    transition: color 0.3s ease;
}

#pathway-container .close-btn:hover {
    color: #1547BB;
}

#result-text {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #1547BB;
}

.pathway-info {
    font-size: 15px;
    font-weight: 400;
    color: #1547BB;
    margin-bottom: 20px;
    line-height: 1.6;
}

.confidence-text,
.discuss-text {
    font-size: 15px;
    font-weight: 400;
    color: #1547BB;
    margin-bottom: 20px;
    line-height: 1.6;
}

#suggested-pathway {
    font-weight: bold;
}

@media (max-width: 768px) {
    #pathway-container {
        width: 95%;
        padding: 15px;
        margin: 20px auto;
        font-size: 13px;
    }

    .confidence-text,
    .discuss-text {
        font-size: 13px;
    }
}

ul.course-list {
    margin-top: 20px;
}




.course-list {
    list-style-type: none;
    font-size: 15px;
    color: #1547BB;
    padding: 0 10px;
    margin: 0 auto 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.course-list li {
    color: #1547BB;
    font-size: 13px;
    font-weight: 400;
    margin: 5px;
    padding: 0 0 0 15px;
    position: relative;
}

.course-list li::before {
    content: "\2022";  /* Unicode for bullet point */
    position: absolute;
    left: 0;
    color: #1547BB;
}

@media (max-width: 768px) {
    .course-list {
        font-size: 13px;
        padding: 0 5px;
    }

    .course-list li {
        font-size: 11px;
        margin: 3px;
        padding: 0 0 0 12px;
    }
}
/* Scrollbar styles - only apply on smaller devices */
@media (max-width: 768px) {
    #success-container,
    #failure-container,
    #confirmation-container,
    #result-container,
    #pathway-container {
        scrollbar-width: thin;
        scrollbar-color: #c1c1c1 #f1f1f1;
    }

    #success-container::-webkit-scrollbar,
    #failure-container::-webkit-scrollbar,
    #confirmation-container::-webkit-scrollbar,
    #result-container::-webkit-scrollbar,
    #pathway-container::-webkit-scrollbar {
        width: 6px;
    }

    #success-container::-webkit-scrollbar-track,
    #failure-container::-webkit-scrollbar-track,
    #confirmation-container::-webkit-scrollbar-track,
    #result-container::-webkit-scrollbar-track,
    #pathway-container::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    #success-container::-webkit-scrollbar-thumb,
    #failure-container::-webkit-scrollbar-thumb,
    #confirmation-container::-webkit-scrollbar-thumb,
    #result-container::-webkit-scrollbar-thumb,
    #pathway-container::-webkit-scrollbar-thumb {
        background-color: #c1c1c1;
        border-radius: 3px;
    }
}

/* Container styles */
.form-container {
    max-width: 500px;
    margin: 2rem auto;
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  /* Heading styles */
  .form-container h2 {
    font-size: 1.25rem;
    color: #1a365d;  /* Darker blue for better contrast */
    margin-bottom: 1.5rem;
    text-align: left;
    font-weight: 500;
  }

  /* Form row styles */
  .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  @media (max-width: 600px) {
    .form-row {
      flex-direction: column;
      gap: 0.75rem;
    }
  }

  /* Form group styles */
  .form-group {
    flex: 1;
    position: relative;
    margin-bottom: 1.5rem;
  }

  /* Input styles */
  .form-group input {
    width: 100%;
    padding: 0.75rem 2rem 0.5rem 0;
    font-size: 0.875rem;
    color: #2d3748;
    border: none;
    border-bottom: 1px solid #e2e8f0;
    background: transparent;
    transition: border-color 0.2s ease;
  }

  .form-group input:focus {
    outline: none;
    border-bottom-color: #3182ce;  /* Professional blue */
  }

  /* Label styles */
  .form-group label {
    position: absolute;
    top: 0.75rem;
    left: 0;
    font-size: 0.875rem;
    color: #718096;  /* Subtle gray */
    pointer-events: none;
    transition: all 0.2s ease;
  }

  /* Floating label effect */
  .form-group input:focus ~ label,
  .form-group input:not(:placeholder-shown) ~ label {
    top: -0.5rem;
    font-size: 0.75rem;
    color: #3182ce;  /* Professional blue */
  }

  /* Validation icon styles */
  #email-validation-icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #718096;
    pointer-events: none;
  }

  /* Submit button styles */
  button#submit-form {
    margin-top: 1.5rem;
    width: 100%;
    padding: 0.75rem;
    background-color: #1547bb;  /* Updated to main blue */
    color: #ffffff;
    font-size: 0.875rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  button#submit-form:hover {
    background-color: #121c41;  /* Updated to dark blue on hover */
  }

  /* Success state */
  .form-group.success input {
    border-bottom-color: #7ae582;  /* Updated to official green */
  }

  .form-group.success label {
    color: #7ae582;  /* Updated to official green */
  }

  /* Error state */
  .form-group.error input {
    border-bottom-color: #e53e3e;  /* Error red */
  }

  .form-group.error label {
    color: #e53e3e;
  }

  /* Optional: Add subtle hover effect on inputs */
  .form-group input:hover {
    border-bottom-color: #cbd5e0;
  }

  .role-suggestions {
    margin-top: 0.5rem;
    position: relative;
    z-index: 10;
  }

  .role-suggestions-scroll {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    max-height: 5.5rem; /* Approximately 2-3 rows */
    overflow-y: auto;
    padding-right: 0.5rem;
    scrollbar-width: thin;
  }

  /* Custom scrollbar styling */
  .role-suggestions-scroll::-webkit-scrollbar {
    width: 4px;
  }

  .role-suggestions-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .role-suggestions-scroll::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
  }

  .role-suggestions-scroll::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
  }

  .suggestion-button {
    font-size: 0.7rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    background-color: #f9fafb;
    color: #4b5563;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    white-space: nowrap;
    height: 1.75rem;
  }

  .suggestion-button:hover {
    background-color: #eff6ff;
    color: #1547bb;
    border-color: #bfdbfe;
  }

  .suggestion-button:focus {
    outline: none;
    ring: 2px;
    ring-color: #1547bb;
    ring-opacity: 0.5;
  }

#terms-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

#terms-checkbox {
    margin-right: 10px;
}

#terms-container a {
    color: #1547BB;
    text-decoration: none;
}

#terms-container a:hover {
    text-decoration: underline;
}

#loading-overlay {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: opacity 0.3s ease;
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
}

.loading-spinner::before,
.loading-spinner::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    animation: pulse 1.8s ease-in-out infinite;
}

.loading-spinner::before {
    width: 100%;
    height: 100%;
    background-color: rgba(52, 152, 219, 0.5);
    animation-delay: -0.9s;
}

.loading-spinner::after {
    width: 75%;
    height: 75%;
    background-color: rgba(52, 152, 219, 0.7);
    top: 12.5%;
    left: 12.5%;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(0);
        opacity: 1;
    }
    50% {
        transform: scale(1);
        opacity: 0.5;
    }
}

.loading-text {
    color: #fff;
    font-size: 0.75rem; /* 12px */
    letter-spacing: 0.5px;
    opacity: 0;
    animation: fadeInOut 2s ease-in-out infinite;
    text-align: center;
}

.lottie-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px; /* Reduced from 60px to 50px */
    height: 50px; /* Reduced from 60px to 50px */
}

.loading-overlay-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem; /* Reduced gap from 1rem to 0.75rem for better proportion */
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

#key-input-container {
    position: relative;
    margin-bottom: 30px;
}

#key-input-container input {
    padding: 10px 15px;
    border: 1px solid #ccc;
    border-radius: 20px;
    background-color: transparent;
    font-size: 14px;
    transition: border-color 0.3s ease;
    width: 50%;
}

@media (max-width: 480px) {
    #key-input-container input::placeholder {
        font-size: 7.5px;
    }
}

#key-input-container input:focus {
    outline: none;
    border-color: #3498db;
}

#key-input-container label {
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    font-size: 13px;
    color: #999;
    transition: all 0.3s ease;
    pointer-events: none;
}

#key-input-container input:focus + label,
#key-input-container input:not(:placeholder-shown) + label {
    top: -18px;
    left: 10px;
    font-size: 12px;
    color: #3498db;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 0 5px;
}

#key-error {
    color: red;
    font-size: 14px;
    margin-top: 5px;
    text-align: center;
}

@keyframes shake {
    0% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-10px);
    }

    50% {
        transform: translateX(10px);
    }

    75% {
        transform: translateX(-10px);
    }

    100% {
        transform: translateX(0);
    }
}

.shake {
    animation: shake 0.5s;
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #333;
}

#consent-container {
    font-family: 'Montserrat', sans-serif;
}

#consent-container h2 {
    color: #1547BB;
    font-size: 14px;
    line-height: 1.5rem;
}

#consent-container p {
    font-size: 14px;
    line-height: 1.125rem;
    color: #333;
}

@media (min-width: 768px) {
    #consent-container p {
        font-size: 14px;
        line-height: 1.25rem;
    }
}

#consent-container p:nth-of-type(2) {
    color: #1547BB;
    font-weight: 500;
}

#consent-container label {
    font-size: 14px;
    color: #555;
}

@media (min-width: 768px) {
    #consent-container label {
        font-size: 14px;
    }
}

#consent-container a {
    color: #1547BB;
    text-decoration: none;
}

#consent-container a:hover {
    text-decoration: underline;
}

#consent-btn {
    background-color: #1547bb;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 9999px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 100%;
}

@media (min-width: 768px) {
    #consent-btn {
        font-size: 14px;
        width: auto;
    }
}

#consent-btn:hover {
    background-color: #121c41;
}

#close-consent-btn {
    transition: color 0.3s ease;
}

#close-consent-btn:hover {
    color: #333;
}

#consent-container .bg-white {
    max-height: 90vh;
    overflow-y: auto;
}

@media (max-width: 640px) {
    #consent-container .bg-white {
        padding: 10px;
    }
}
#results-summary-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 90%;
    padding: 20px;
    z-index: 9999;
    font-family: 'Montserrat', sans-serif;
    color: #333333;
}

@media (max-width: 768px) {
    #results-summary-container {
        padding: 20px;
    }
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.summary-header h2 {
    font-size: 16px;
    font-weight: 700;
    color: #1547BB;
    margin: 0;
}

.close-btn {
    font-size: 24px;
    color: #aaa;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #888;
}

.score-summary {
    text-align: center;
    margin-bottom: 30px;
}

.score-summary h4 {
    font-size: 14px;
    font-weight: 400;
    color: #1547BB;
    margin-bottom: 10px;
}

.score-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: #f2f2f2;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 20px;
    background-color: #f2f2f2;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background-color: #1547bb;
    transition: width 0.5s ease;
}

.remarks {
    font-size: 14px;
    color: #1547BB;
    margin-bottom: 30px;
    line-height: 1.6;
}

.section-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.section {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

#results-summary-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--background-light);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    padding: 30px;
    z-index: 9999;
    font-family: 'Montserrat', sans-serif;
    color: var(--text-dark);
    display: flex;
    flex-direction: column;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--glass-border);
}

.summary-header h2 {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-blue);
    margin: 0;
}

.close-btn {
    font-size: 24px;
    color: var(--text-light);
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: var(--text-dark);
}

.summary-content {
    flex-grow: 1;
    overflow-y: auto;
    padding-right: 15px;
}

.section-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.section {
    background-color: var(--glass-background);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    border: 1px solid var(--glass-border);
    box-shadow: 0 4px 6px var(--glass-shadow);
}

.section:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px var(--glass-shadow);
}

.section h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 15px;
}

.score-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 600;
}

.user-score {
    color: var(--secondary-blue);
}

.total-questions {
    margin: 0 5px;
    color: var(--text-light);
}

.max-score {
    color: var(--text-light);
}

.progress-bar {
    height: 8px;
    background-color: var(--glass-background);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background-color: var(--secondary-blue);
    transition: width 0.5s ease;
    background-color: #1547bb;
}

.remarks {
    font-size: 14px;
    text-align: center;
    line-height: 1.5;
    color: var(--text-light);
}

.summary-content::-webkit-scrollbar {
    width: 8px;
}

.summary-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.summary-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.summary-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.summary-content {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

@media (max-width: 768px) {
    #results-summary-container {
        padding: 20px;
    }

    .summary-header h2 {
        font-size: 14px;
    }

    .section h3 {
        font-size: 14px;
    }

    .score-container {
        font-size: 14px;
    }
}

#quiz-container {
    width: 90%;
    max-width: 800px;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: box-shadow 0.3s ease;
    font-weight: 400;
    font-size: 14px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

@media (max-width: 768px) {
    #quiz-container {
        padding: 15px;
        width: 95%;
    }

    #options-container {
        grid-template-columns: 1fr !important;
    }

    .option-btn {
        font-size: 13px;
        padding: 10px 12px;
        white-space: normal;
        height: auto;
        min-height: 44px;
    }
}

#question {
    font-size: 16px;
    color: #333;
    font-weight: 450;
    margin-top: 10px;
    margin-bottom: 20px;
    font-family: 'Montserrat', sans-serif;
}

.option-btn {
    background-color: #1547bb;
    color: #fff;
    padding: 10px 18px;
    margin: 5px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 14px;
}

.option-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

#next-btn {
    background-color: #7ae582;
    color: #fff;
    padding: 10px 18px;
    margin: 5px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 14px;
}

#next-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

#progress-bar {
    height: 20px;
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
}

#progress-bar-fill {
    display: block;
    height: 100%;
    width: 0;
    background: #1547bb;
    transition: width 0.5s ease-in-out;
}



.action-button {
    display: inline-block;
    padding: 12px 24px;
    margin: 10px;
    font-size: 15px;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    border-radius: 50px;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    outline: none;
}



#next-section-btn {
    background-color: #3498db;
    color: #ffffff;
    border: 2px solid #3498db;
}

#next-section-btn:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#restart-btn {
    background-color: #ffffff;
    color: #3498db;
    border: 2px solid #3498db;
}

#restart-btn:hover {
    background-color: #3498db;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .action-button {
        display: block;
        width: 80%;
        margin: 10px auto;
    }
}

#result-container {
    width: 95%;
    max-width: 800px;
    padding: 20px;
    border-radius: 20px;
    text-align: center;
    margin: 20px auto;
    font-family: 'Montserrat', sans-serif;
    color: #333333;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    max-height: 90vh;
}

@media (max-width: 768px) {
    #result-container {
        padding: 15px;
        font-size: 12px;
    }
}

#result-container h2#result-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    line-height: 1.4;
    text-transform: uppercase;
    color: #1547BB;
}

#result-container h3#remarks {
    font-size: 15px;
    font-weight: 700;
    margin-bottom: 20px;
    text-transform: uppercase;
    color: #1547BB;
}

#result-container p {
    font-size: 14px;
    font-weight: 400;
    color: #1447BA;
    margin-bottom: 20px;
    line-height: 1.6;
}

@media (max-width: 768px) {
    #result-container h2#result-text {
        font-size: 14px;
    }

    #result-container h3#remarks {
        font-size: 13px;
    }

    #result-container p {
        font-size: 12px;
        line-height: 1.5;
    }
}

#result-container .book-btn,
.book-btn {
    font-size: 14px;
    font-weight: bold;
    padding: 12px 24px;
    margin-top: 20px;
    background-color: #1547bb;
    color: #ffffff;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: inline-block;
}

@media (max-width: 768px) {
    #result-container .book-btn,
    .book-btn {
        font-size: 12px;
        padding: 10px 20px;
    }
}

#result-container .book-btn:hover,
.book-btn:hover {
    background-color: #121c41;
}

#result-container .book-text,
.book-text {
    font-size: 14px;
    font-weight: 700;
    color: #1447BA;
    margin-top: 15px;
    display: block;
}

@media (max-width: 768px) {
    #result-container .book-text,
    .book-text {
        font-size: 12px;
    }
}

#result-container .book-text .regular-text,
.book-text .regular-text {
    font-size: 14px;
    font-weight: 400;
    text-decoration: none;
    color: #1447BA;
    display: block;
    margin-top: 10px;
}

@media (max-width: 768px) {
    #result-container .book-text .regular-text,
    .book-text .regular-text {
        font-size: 12px;
    }
}

#result-container .book-text a,
.book-text a,
.book-link {
    text-decoration: underline;
    color: inherit;
    transition: color 0.3s ease;
}

#result-container .book-text a:hover,
.book-text a:hover,
.book-link:hover {
    color: #1547bb;
}

/* Scrollbar styles for the result container */
#result-container::-webkit-scrollbar {
    width: 6px;
}

#result-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#result-container::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 3px;
}

#result-container::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8;
}

#result-container {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}



.notification {
    position: fixed;
    top: 16px;
    right: 16px;
    z-index: 50;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 24rem;
    transition: opacity 300ms ease-in-out;
    opacity: 0;
}

.notification.success {
    background-color: #f0fff4;
    border: 1px solid #68d391;
    color: #2f855a;
}

.notification.error {
    background-color: #fff5f5;
    border: 1px solid #fc8181;
    color: #c53030;
}

.feedback-container, .feedback-form {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 24px;
    max-width: 600px;
    margin: 20px auto;
    font-family: 'Montserrat', sans-serif;
  }

  .feedback-container h3, .feedback-form h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1547BB;
    margin-bottom: 20px;
    text-align: center;
  }

  .feedback-btn, .submit-feedback {
    background-color: #1547BB;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: inline-block;
    margin: 0 10px;
  }

  .feedback-btn:hover, .submit-feedback:hover {
    background-color: #121c41;
  }

  .feedback-question {
    margin-bottom: 24px;
  }

  .feedback-question p {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
  }

  .rating-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .rating-container label {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: transform 0.2s ease;
    margin: 0 5px;
  }

  .rating-container label:hover {
    transform: translateY(-2px);
  }

  .rating-container input[type="radio"] {
    display: none;
  }

  .rating-container span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    min-width: 80px;
    height: 40px;
    border-radius: 20px;
    border: 2px solid #1547BB;
    font-size: 12px;
    font-weight: 600;
    color: #1547BB;
    padding: 0 10px;
    margin-bottom: 4px;
    transition: all 0.2s ease;
    text-align: center;
  }

  .rating-container input[type="radio"]:checked + span {
    background-color: #1547BB;
    color: #ffffff;
  }

  @media (max-width: 768px) {
    .feedback-container, .feedback-form {
      padding: 20px;
    }

    .rating-container {
      flex-direction: column;
      align-items: stretch;
    }

    .rating-container label {
      margin-bottom: 10px;
    }

    .rating-container span {
      width: 100%;
      height: 40px;
      font-size: 12px;
    }
  }

  #pathway-btn {
    display: none;
    background-color: #1547bb;
    color: #ffffff;
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

#pathway-btn:hover {
    background-color: #121c41;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    #pathway-btn {
        font-size: 13px;
        padding: 10px 20px;
    }
}

/* Ensure the button is a block-level element on smaller screens for better spacing */
@media (max-width: 480px) {
    #pathway-btn {
        display: block;
        width: 80%;
        margin: 20px auto;
    }
}

.validation-spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 24px;
    border-radius: 6px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 9999;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    font-weight: 500;
  }

  .notification.success {
    background-color: #48bb78;
    color: white;
  }

  .notification.error {
    background-color: #FEE2E2;
    color: #991B1B;
    border: 1px solid #FCA5A5;
  }

  #role-validation-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #718096;
    pointer-events: none;
}

.form-group {
    position: relative;
}

/* Container for role suggestions */
.role-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    max-height: 150px;
    background: #ffffff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    z-index: 10;
    display: none;
    padding: 0.75rem 0.5rem;
    overflow: hidden; /* Changed from overflow-y: auto and overflow-x: hidden */
}

/* New mobile-optimized class for role suggestions */
.role-suggestions-scroll {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    max-height: 120px;
    overflow-y: auto;
    padding-right: 0.5rem;
    scrollbar-width: thin;
}

/* Update suggestion-tab styles for better mobile compatibility */
.suggestion-tab {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    color: #4a5568;
    background-color: #f3f4f6;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0.25rem;
    border: 1px solid #e5e7eb;
}

/* Hover effect for tabs */
.suggestion-tab:hover {
    background: #3182ce; /* Blue background on hover */
    color: #ffffff; /* White text on hover */
    transform: scale(1.05); /* Slightly enlarge tab */
}

/* Scrollbar customization */
.role-suggestions-scroll::-webkit-scrollbar {
    width: 4px;
    height: 4px; /* Added for horizontal scrolling */
}

.role-suggestions-scroll::-webkit-scrollbar-thumb {
    background-color: #cbd5e0;
    border-radius: 3px;
}

.role-suggestions-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Mobile-specific styles */
@media (max-width: 768px) {
    .role-suggestions {
        padding: 0.5rem;
    }

    /* Mobile-optimized layout - stacked suggestion tabs */
    .role-suggestions-scroll {
        display: flex;
        flex-direction: column; /* Stack tabs vertically on mobile */
        align-items: stretch;
        max-height: 150px;
        overflow-y: auto;
        gap: 0.25rem;
    }

    .suggestion-tab {
        width: 100%; /* Full width on mobile */
        white-space: normal; /* Allow text wrapping */
        height: auto;
        padding: 0.4rem 0.75rem;
        margin: 0.1rem 0;
        font-size: 0.7rem;
        text-overflow: ellipsis;
        overflow: hidden;
        display: block;
    }
}

/* Loading Overlay Styles */
.quiz-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(17, 24, 39, 0.95);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .quiz-loading-content {
    text-align: center;
    color: white;
  }

  .quiz-spinner-container {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
  }

  .quiz-circular-progress {
    transform: rotate(-90deg);
  }

  .quiz-progress-background,
  .quiz-progress-bar {
    fill: none;
    stroke-width: 8;
  }

  .quiz-progress-background {
    stroke: rgba(255, 255, 255, 0.1);
  }

  .quiz-progress-bar {
    stroke: #1547bb;
    stroke-dasharray: 339.292; /* Circumference of the circle (2 * PI * r) */
    stroke-dashoffset: 339.292; /* Start at 0% progress */
    transition: stroke-dashoffset 0.3s ease;
  }

  .quiz-progress-percentage {
    position: absolute;
    top: 0;
    left: 0;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
  }

  .quiz-loading-text {
    font-size: 1.25rem;
    margin-top: 2rem;
    color: #E5E7EB;
    opacity: 0.9;
    font-weight: 500;
  }

  .quiz-fade-in {
    opacity: 1;
  }

  #pathway-btn {
  position: relative;
  overflow: hidden;
}

.button-loading-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.5);
  transition: width 20s linear;
}

#pathway-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

#pathway-btn:disabled .button-text {
  opacity: 0.7;
}

/* Modal Loading Overlay */
#modal-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(17, 24, 39, 0.95);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-loading-content {
  text-align: center;
  color: white;
}

.modal-spinner-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 20px;
}

.modal-circular-progress {
  transform: rotate(-90deg);
}

.modal-progress-background,
.modal-progress-bar {
  fill: none;
  stroke-width: 8;
}

.modal-progress-background {
  stroke: rgba(255, 255, 255, 0.1);
}

.modal-progress-bar {
  stroke: #1547bb;
  stroke-dasharray: 339.292;
  stroke-dashoffset: 339.292;
  transition: stroke-dashoffset 0.3s ease;
}

.modal-progress-percentage {
  position: absolute;
  top: 0;
  left: 0;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.modal-loading-text {
  font-size: 1.25rem;
  margin-top: 2rem;
  color: #E5E7EB;
  opacity: 0.9;
  font-weight: 500;
}

#pathway-btn {
  position: relative;
  min-width: 200px; /* Ensure enough space for text */
  transition: all 0.3s ease;
}

#pathway-btn .button-text {
  position: relative;
  z-index: 2;
  transition: opacity 0.3s ease;
}

#pathway-btn:disabled .button-text {
  opacity: 0.7;
}

.processing-text {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-text {
    color: #fff;
    font-size: 0.75rem; /* 12px */
    letter-spacing: 0.5px;
    opacity: 0;
    animation: fadeInOut 2s ease-in-out infinite;
    text-align: center;
}

.lottie-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px; /* Reduced from 60px to 50px */
    height: 50px; /* Reduced from 60px to 50px */
    margin-bottom: 0.5rem;
}

/* Start page fade-in animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -45%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* Add fade-in for the start page elements */
@keyframes contentFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Self-assessment specific styling */
.self-assessment-indicator {
    display: inline-block;
    background-color: #e9f4ff;
    color: #1e40af;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .response-recorded {
    display: inline-block;
    background-color: #ecfdf5;
    color: #047857;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  /* Option styling for self-assessment */
  .self-assessment .option-btn {
    position: relative;
    background-color: #f3f4f6;
    color: #1f2937;
    border: 1px solid #d1d5db;
    padding-left: 2.5rem;
    text-align: left;
  }

  .self-assessment .option-btn:hover {
    background-color: #e5e7eb;
  }

  /* Level indicator circles */
  .self-assessment .option-btn::before {
    content: "";
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
  }

  /* Level colors */
  .self-assessment .option-btn[data-level="1"]::before {
    background-color: #fecaca; /* Light red for basic */
  }

  .self-assessment .option-btn[data-level="2"]::before {
    background-color: #fde68a; /* Light yellow for intermediate */
  }

  .self-assessment .option-btn[data-level="3"]::before {
    background-color: #a7f3d0; /* Light green for advanced */
  }

  /* Selected option in self-assessment */
  .option-btn.self-assessment-selected {
    background-color: #dbeafe !important;
    border: 2px solid #3b82f6 !important;
    color: #1e40af !important;
    font-weight: 500;
  }

  .option-btn.self-assessment-selected::before {
    width: 0.85rem;
    height: 0.85rem;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }

  /* Modern Form Styles */
  .modern-form-container {
    max-width: 600px;
    width: 90%;
    margin: 1rem auto;
    max-height: 90vh;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .form-header {
    text-align: center;
    padding: 1.5rem 2rem 1rem 2rem;
    flex-shrink: 0;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  }

  .form-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
  }

  .form-header p {
    color: #64748b;
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .form-content {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem 2rem;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 transparent;
  }

  .form-content::-webkit-scrollbar {
    width: 6px;
  }

  .form-content::-webkit-scrollbar-track {
    background: transparent;
  }

  .form-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .form-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  .modern-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-footer {
    flex-shrink: 0;
    padding: 1rem 2rem 1.5rem 2rem;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    background: rgba(248, 250, 252, 0.5);
  }

  .form-section {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #334155;
    margin-bottom: 0.25rem;
  }

  .input-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .input-group {
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .modern-input, .modern-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 0.9rem;
    background-color: #ffffff;
    color: #334155;
    transition: all 0.2s ease;
    outline: none;
  }

  .modern-input:focus, .modern-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
  }

  .modern-input::placeholder {
    color: #94a3b8;
  }

  .validation-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .input-hint {
    font-size: 0.8rem;
    color: #64748b;
    margin-top: 0.25rem;
    line-height: 1.4;
  }

  /* User Type Selector Cards */
  .user-type-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .type-card {
    position: relative;
    display: block;
    padding: 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .type-card:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  }

  .type-card input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
  }

  .type-card input[type="radio"]:checked + .card-content {
    color: #3b82f6;
  }

  .type-card input[type="radio"]:checked ~ .card-check {
    opacity: 1;
    transform: scale(1);
  }

  .type-card input[type="radio"]:checked ~ .card-content .card-icon {
    transform: scale(1.1);
  }

  .card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.5rem;
    transition: color 0.2s ease;
  }

  .card-icon {
    font-size: 1.75rem;
    transition: transform 0.2s ease;
  }

  .card-title {
    font-weight: 600;
    font-size: 1rem;
    color: #1e293b;
  }

  .card-description {
    font-size: 0.85rem;
    color: #64748b;
    line-height: 1.4;
  }

  .card-check {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    background: #3b82f6;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
  }

  .card-check::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
  }

  /* Dynamic Sections */
  .dynamic-section {
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  /* Form Actions */
  .form-actions {
    display: flex;
    justify-content: center;
  }

  .modern-submit-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 2rem;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    min-height: 44px;
  }

  .modern-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
  }

  .modern-submit-btn:active {
    transform: translateY(0);
  }

  .btn-icon {
    font-size: 1.1rem;
    transition: transform 0.2s ease;
  }

  .modern-submit-btn:hover .btn-icon {
    transform: translateX(2px);
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .modern-form-container {
      margin: 0.5rem auto;
      width: 95%;
      max-height: 95vh;
    }

    .form-header {
      padding: 1rem 1.5rem 0.75rem 1.5rem;
    }

    .form-content {
      padding: 1rem 1.5rem;
    }

    .form-footer {
      padding: 0.75rem 1.5rem 1rem 1.5rem;
    }

    .input-grid {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .user-type-selector {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .form-header h2 {
      font-size: 1.25rem;
    }

    .form-header p {
      font-size: 0.85rem;
    }

    .type-card {
      padding: 1rem;
    }

    .card-content {
      gap: 0.4rem;
    }

    .card-icon {
      font-size: 1.5rem;
    }

    .card-title {
      font-size: 0.9rem;
    }

    .card-description {
      font-size: 0.8rem;
    }

    .modern-form {
      gap: 1.25rem;
    }

    .form-section {
      gap: 0.5rem;
    }
  }

  @media (max-width: 480px) {
    .modern-form-container {
      margin: 0.25rem auto;
      width: 98%;
      max-height: 98vh;
    }

    .form-header {
      padding: 0.75rem 1rem 0.5rem 1rem;
    }

    .form-content {
      padding: 0.75rem 1rem;
    }

    .form-footer {
      padding: 0.5rem 1rem 0.75rem 1rem;
    }

    .form-header h2 {
      font-size: 1.1rem;
    }

    .form-header p {
      font-size: 0.8rem;
    }

    .modern-input, .modern-select {
      padding: 0.65rem 0.75rem;
      font-size: 0.85rem;
    }

    .modern-submit-btn {
      width: 100%;
      justify-content: center;
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }

    .section-title {
      font-size: 0.9rem;
    }

    .type-card {
      padding: 0.75rem;
    }

    .card-icon {
      font-size: 1.25rem;
    }

    .card-title {
      font-size: 0.85rem;
    }

    .card-description {
      font-size: 0.75rem;
    }
  }

  /* Landscape mobile optimization */
  @media (max-height: 600px) and (orientation: landscape) {
    .modern-form-container {
      max-height: 95vh;
    }

    .form-header {
      padding: 0.75rem 2rem 0.5rem 2rem;
    }

    .form-header h2 {
      font-size: 1.25rem;
      margin-bottom: 0.25rem;
    }

    .form-header p {
      font-size: 0.8rem;
    }

    .form-content {
      padding: 1rem 2rem;
    }

    .modern-form {
      gap: 1rem;
    }

    .form-section {
      gap: 0.5rem;
    }

    .type-card {
      padding: 1rem;
    }

    .card-content {
      gap: 0.4rem;
    }
  }

  /* English Proficiency Assessment Styles */
  #english-assessment-container {
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    overflow: hidden;
  }

  .english-assessment-wrapper {
    max-width: 800px;
    width: 100%;
    height: 95vh;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .english-header {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 1rem 1.5rem;
    text-align: center;
    flex-shrink: 0;
  }

  .english-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
  }

  .english-header p {
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0;
  }

  .timer-container {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 0.75rem 1.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
  }

  .timer-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    font-weight: 600;
  }

  .timer-label {
    color: #64748b;
  }

  .timer-value {
    color: #dc2626;
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    border: 2px solid #fecaca;
  }

  .timer-value.warning {
    color: #ea580c;
    border-color: #fed7aa;
    animation: pulse 1s infinite;
  }

  .timer-value.critical {
    color: #dc2626;
    border-color: #fecaca;
    animation: pulse 0.5s infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .english-question-container {
    padding: 1rem 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .question-text {
    margin-bottom: 1rem;
    text-align: center;
    flex-shrink: 0;
  }

  .question-text h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
  }

  .question-text p {
    font-size: 0.875rem;
    color: #64748b;
    line-height: 1.5;
    margin: 0;
  }

  .visual-aid-container {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
    flex-shrink: 0;
  }

  .seasons-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    max-height: 120px;
    width: auto;
  }

  .response-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .response-label {
    display: block;
    font-size: 0.95rem;
    font-weight: 600;
    color: #334155;
    margin-bottom: 0.5rem;
    flex-shrink: 0;
  }

  .english-textarea {
    width: 100%;
    flex: 1;
    min-height: 150px;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .english-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .english-textarea::placeholder {
    color: #94a3b8;
    font-style: italic;
  }

  .character-count {
    text-align: right;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #64748b;
    flex-shrink: 0;
  }

  .english-actions {
    display: flex;
    justify-content: center;
    padding-top: 1rem;
    flex-shrink: 0;
  }

  .english-submit-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 3px 10px rgba(59, 130, 246, 0.3);
  }

  .english-submit-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
  }

  .english-submit-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  }

  .btn-icon {
    font-size: 1.1rem;
    transition: transform 0.2s ease;
  }

  .english-submit-btn:hover:not(:disabled) .btn-icon {
    transform: translateX(2px);
  }

  /* English Assessment Results Styles */
  #english-success-container,
  #english-completion-container {
    height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .results-wrapper {
    max-width: 700px;
    width: 100%;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .results-header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 2rem;
    text-align: center;
  }

  .results-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .score-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  .score-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: #fbbf24;
  }

  .score-level {
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.9;
  }

  .results-content {
    padding: 2rem;
  }

  .feedback-section {
    margin-bottom: 2rem;
  }

  .feedback-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.5rem;
  }

  .feedback-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .feedback-item {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
  }

  .feedback-item h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .feedback-item p {
    font-size: 0.9rem;
    color: #4b5563;
    line-height: 1.5;
  }

  .overall-feedback {
    background: #eff6ff;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #dbeafe;
  }

  .overall-feedback h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 0.75rem;
  }

  .overall-feedback p {
    font-size: 0.95rem;
    color: #374151;
    line-height: 1.6;
  }

  .strengths-improvements {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .strengths-section,
  .improvements-section {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
  }

  .strengths-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #059669;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .strengths-section h4::before {
    content: "✓";
    background: #10b981;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
  }

  .improvements-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #dc2626;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .improvements-section h4::before {
    content: "!";
    background: #ef4444;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
  }

  .strengths-section ul,
  .improvements-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .strengths-section li,
  .improvements-section li {
    font-size: 0.9rem;
    color: #4b5563;
    line-height: 1.5;
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
  }

  .strengths-section li::before {
    content: "•";
    color: #10b981;
    font-weight: bold;
    position: absolute;
    left: 0;
  }

  .improvements-section li::before {
    content: "•";
    color: #ef4444;
    font-weight: bold;
    position: absolute;
    left: 0;
  }

  .next-steps {
    background: #fef3c7;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #fbbf24;
    margin-bottom: 2rem;
  }

  .next-steps h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #92400e;
    margin-bottom: 0.75rem;
  }

  .next-steps p {
    font-size: 0.95rem;
    color: #78350f;
    line-height: 1.6;
  }

  .results-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }

  .results-btn {
    padding: 0.875rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 200px;
  }

  .results-btn.primary {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }

  .results-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
  }

  .results-btn.secondary {
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
  }

  .results-btn.secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(107, 114, 128, 0.4);
  }

  /* Mobile Responsive for English Assessment */
  @media (max-width: 768px) {
    #english-assessment-container,
    #english-success-container,
    #english-completion-container {
      padding: 0.5rem;
    }

    .results-wrapper {
      max-width: 100%;
    }

    .results-header {
      padding: 1.5rem 1rem;
    }

    .results-content {
      padding: 1.5rem 1rem;
    }

    .feedback-grid {
      grid-template-columns: 1fr;
    }

    .strengths-improvements {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .results-actions {
      flex-direction: column;
      gap: 0.75rem;
    }

    .results-btn {
      min-width: auto;
      width: 100%;
    }

    .english-assessment-wrapper {
      height: 98vh;
    }

    .english-header,
    .completion-header {
      padding: 1rem;
    }

    .english-header h2,
    .completion-header h2 {
      font-size: 1.125rem;
    }

    .english-header p {
      font-size: 0.8rem;
    }

    .english-question-container,
    .completion-content {
      padding: 1rem;
    }

    .question-text h3 {
      font-size: 1rem;
    }

    .question-text p {
      font-size: 0.8rem;
    }

    .english-textarea {
      min-height: 120px;
      font-size: 0.85rem;
      padding: 0.5rem;
    }

    .timer-display {
      font-size: 0.85rem;
    }

    .timer-value {
      font-size: 1rem;
    }

    .seasons-image {
      max-height: 80px;
    }

    .response-label {
      font-size: 0.85rem;
    }

    .character-count {
      font-size: 0.7rem;
    }

    .english-submit-btn {
      padding: 0.625rem 1.25rem;
      font-size: 0.85rem;
    }
  }

  @media (max-width: 480px) {
    #english-assessment-container,
    #english-success-container,
    #english-completion-container {
      padding: 0.25rem;
    }

    .results-wrapper {
      border-radius: 12px;
    }

    .results-header {
      padding: 1rem 0.75rem;
    }

    .results-content {
      padding: 1rem 0.75rem;
    }

    .score-value {
      font-size: 2rem;
    }

    .feedback-section h3 {
      font-size: 1.125rem;
    }

    .feedback-item {
      padding: 0.75rem;
    }

    .overall-feedback {
      padding: 1rem;
    }

    .strengths-section,
    .improvements-section {
      padding: 1rem;
    }

    .next-steps {
      padding: 1rem;
    }

    .english-assessment-wrapper {
      height: 99vh;
      border-radius: 12px;
    }

    .english-header,
    .completion-header {
      padding: 0.75rem;
    }

    .english-header h2,
    .completion-header h2 {
      font-size: 1rem;
    }

    .english-header p {
      font-size: 0.75rem;
    }

    .english-question-container,
    .completion-content {
      padding: 0.75rem;
    }

    .question-text h3 {
      font-size: 0.95rem;
    }

    .question-text p {
      font-size: 0.75rem;
    }

    .english-textarea {
      min-height: 100px;
      font-size: 0.8rem;
      padding: 0.5rem;
    }

    .english-submit-btn,
    .completion-btn {
      width: 100%;
      justify-content: center;
      padding: 0.75rem;
      font-size: 0.8rem;
    }

    .timer-container {
      padding: 0.5rem;
    }

    .timer-display {
      font-size: 0.8rem;
    }

    .timer-value {
      font-size: 0.9rem;
      padding: 0.2rem 0.4rem;
    }

    .seasons-image {
      max-height: 60px;
    }

    .visual-aid-container {
      margin-bottom: 0.75rem;
    }

    .question-text {
      margin-bottom: 0.75rem;
    }

    .response-label {
      font-size: 0.8rem;
      margin-bottom: 0.25rem;
    }

    .character-count {
      font-size: 0.65rem;
      margin-top: 0.25rem;
    }

    .english-actions {
      padding-top: 0.75rem;
    }
  }

  /* Landscape mobile and very small screens fallback */
  @media (max-height: 600px) and (orientation: landscape) {
    .english-assessment-wrapper {
      height: auto;
      max-height: 95vh;
      overflow-y: auto;
    }

    .english-question-container {
      overflow-y: auto;
    }

    .english-textarea {
      min-height: 80px;
    }

    .seasons-image {
      max-height: 50px;
    }

    .english-header {
      padding: 0.5rem 1rem;
    }

    .english-header h2 {
      font-size: 1rem;
    }

    .english-header p {
      font-size: 0.75rem;
    }

    .timer-container {
      padding: 0.5rem;
    }

    .question-text {
      margin-bottom: 0.5rem;
    }

    .visual-aid-container {
      margin-bottom: 0.5rem;
    }
  }

  /* Very small height screens */
  @media (max-height: 500px) {
    #english-assessment-container {
      align-items: flex-start;
      padding-top: 1rem;
    }

    .english-assessment-wrapper {
      height: auto;
      max-height: calc(100vh - 2rem);
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .english-textarea {
      min-height: 60px;
    }

    .seasons-image {
      max-height: 40px;
    }
  }