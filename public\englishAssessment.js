/**
 * English Proficiency Assessment Module
 * Handles the English assessment flow for student users
 */

class EnglishAssessment {
  constructor() {
    this.timeLimit = 30 * 60; // 30 minutes in seconds
    this.timeRemaining = this.timeLimit;
    this.timerInterval = null;
    this.isSubmitted = false;
    this.minResponseLength = 50; // Minimum characters for submission
  }

  /**
   * Initialize the English assessment
   */
  async init() {
    this.resetAssessment();
    await this.checkIfRetake();
    this.setupEventListeners();
    this.startTimer();
    this.updateCharacterCount();
    console.log('English assessment initialized');
  }

  /**
   * Check if this is a retake and show appropriate message
   */
  async checkIfRetake() {
    try {
      const email = document.getElementById('email').value.trim();

      if (!email || typeof db === 'undefined' || typeof userCompany === 'undefined') {
        return;
      }

      const companyRef = db.collection('companies').doc(userCompany);
      const userRef = companyRef.collection('users').doc(email);
      const userDoc = await userRef.get();
      const userData = userDoc.data();

      if (userData && userData.englishAssessmentCompleted && userData.englishProficiencyScore < 16) {
        // This is a retake - show retake message
        this.showRetakeMessage(userData.englishProficiencyScore, userData.englishProficiencyLevel);
      }
    } catch (error) {
      console.error('Error checking retake status:', error);
      // Continue with normal assessment if check fails
    }
  }

  /**
   * Show retake message to encourage the student
   */
  showRetakeMessage(previousScore, previousLevel) {
    const headerElement = document.querySelector('.english-header p');
    if (headerElement) {
      headerElement.innerHTML = `
        <strong>Retake Opportunity:</strong> Your previous score was ${previousScore}/21 (${previousLevel} level).
        You need 16+ points to proceed to digital skills assessment. Take your time and show your best English skills!
      `;
      headerElement.style.background = 'rgba(255, 255, 255, 0.1)';
      headerElement.style.padding = '0.5rem';
      headerElement.style.borderRadius = '4px';
      headerElement.style.marginTop = '0.5rem';
    }
  }

  /**
   * Reset assessment state for retakes
   */
  resetAssessment() {
    // Reset timer
    this.timeRemaining = this.timeLimit;
    this.isSubmitted = false;

    // Clear any existing timer
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }

    // Clear textarea
    const textarea = document.getElementById('english-response');
    if (textarea) {
      textarea.value = '';
    }

    // Reset submit button
    const submitBtn = document.getElementById('submit-english-assessment');
    if (submitBtn) {
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<span class="btn-text">Submit Assessment</span><span class="btn-icon">→</span>';
    }

    console.log('English assessment reset for retake');
  }

  /**
   * Setup event listeners for the assessment
   */
  setupEventListeners() {
    const textarea = document.getElementById('english-response');
    const submitBtn = document.getElementById('submit-english-assessment');
    const returnBtn = document.getElementById('return-to-portal');

    // Character count and submit button state
    textarea.addEventListener('input', () => {
      this.updateCharacterCount();
      this.updateSubmitButtonState();
    });

    // Submit assessment
    submitBtn.addEventListener('click', () => {
      this.submitAssessment();
    });

    // Return to portal button
    if (returnBtn) {
      returnBtn.addEventListener('click', () => {
        window.location.href = 'https://barefootelearning.etraininglibrary.com/';
      });
    }

    // Prevent accidental page refresh
    window.addEventListener('beforeunload', (e) => {
      if (!this.isSubmitted && this.timeRemaining < this.timeLimit) {
        e.preventDefault();
        e.returnValue = 'Your English assessment is in progress. Are you sure you want to leave?';
      }
    });
  }

  /**
   * Start the countdown timer
   */
  startTimer() {
    this.updateTimerDisplay();
    
    this.timerInterval = setInterval(() => {
      this.timeRemaining--;
      this.updateTimerDisplay();
      
      if (this.timeRemaining <= 0) {
        this.timeUp();
      }
    }, 1000);
  }

  /**
   * Update the timer display
   */
  updateTimerDisplay() {
    const timerElement = document.getElementById('english-timer');
    const minutes = Math.floor(this.timeRemaining / 60);
    const seconds = this.timeRemaining % 60;
    
    const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    timerElement.textContent = timeString;
    
    // Add warning classes based on time remaining
    timerElement.classList.remove('warning', 'critical');
    
    if (this.timeRemaining <= 300) { // 5 minutes
      timerElement.classList.add('critical');
    } else if (this.timeRemaining <= 600) { // 10 minutes
      timerElement.classList.add('warning');
    }
  }

  /**
   * Handle time up scenario
   */
  timeUp() {
    clearInterval(this.timerInterval);
    
    const textarea = document.getElementById('english-response');
    const response = textarea.value.trim();
    
    if (response.length >= this.minResponseLength) {
      // Auto-submit if there's sufficient content
      this.submitAssessment();
    } else {
      // Show time up message and force submission
      alert('Time is up! Your response will be submitted as is.');
      this.submitAssessment();
    }
  }

  /**
   * Update character count display
   */
  updateCharacterCount() {
    const textarea = document.getElementById('english-response');
    const charCountElement = document.getElementById('char-count');
    const currentLength = textarea.value.length;
    
    charCountElement.textContent = currentLength;
    
    // Change color based on length
    if (currentLength >= 4500) {
      charCountElement.style.color = '#dc2626'; // Red
    } else if (currentLength >= 4000) {
      charCountElement.style.color = '#ea580c'; // Orange
    } else {
      charCountElement.style.color = '#64748b'; // Default gray
    }
  }

  /**
   * Update submit button state based on response length
   */
  updateSubmitButtonState() {
    const textarea = document.getElementById('english-response');
    const submitBtn = document.getElementById('submit-english-assessment');
    const response = textarea.value.trim();
    
    if (response.length >= this.minResponseLength) {
      submitBtn.disabled = false;
    } else {
      submitBtn.disabled = true;
    }
  }

  /**
   * Submit the English assessment
   */
  async submitAssessment() {
    if (this.isSubmitted) return;
    
    const textarea = document.getElementById('english-response');
    const response = textarea.value.trim();
    
    if (response.length < this.minResponseLength) {
      alert(`Please write at least ${this.minResponseLength} characters before submitting.`);
      return;
    }

    this.isSubmitted = true;
    clearInterval(this.timerInterval);
    
    // Show loading state
    const submitBtn = document.getElementById('submit-english-assessment');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="btn-text">Analyzing...</span>';
    submitBtn.disabled = true;

    try {
      // Get user information
      const email = document.getElementById('email').value.trim();
      const userType = document.querySelector('input[name="user-type"]:checked')?.value;
      const studentLevel = document.getElementById('student-level').value;
      
      // Send to AI for analysis
      const analysisResult = await this.analyzeEnglishProficiency(response, email, studentLevel);
      
      // Store results in database
      await this.storeEnglishAssessmentResults(email, response, analysisResult);
      
      // Determine next step based on score
      if (analysisResult.score >= 16) {
        // L2/GCSE level - show success results then proceed to digital skills assessment
        this.showSuccessResults(analysisResult);
      } else {
        // Below L2 - show detailed completion message with improvement guidance
        this.showCompletionMessage(analysisResult);
      }
      
    } catch (error) {
      console.error('Error submitting English assessment:', error);
      alert('An error occurred while processing your assessment. Please try again.');
      
      // Reset button state
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
      this.isSubmitted = false;
    }
  }

  /**
   * Send response to AI for analysis
   */
  async analyzeEnglishProficiency(response, email, studentLevel) {
    const analysisResponse = await fetch('/api/analyze-english-proficiency', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        response: response,
        email: email,
        studentLevel: studentLevel,
        timeSpent: this.timeLimit - this.timeRemaining
      }),
    });

    if (!analysisResponse.ok) {
      throw new Error(`Analysis failed: ${analysisResponse.statusText}`);
    }

    return await analysisResponse.json();
  }

  /**
   * Store English assessment results in database
   */
  async storeEnglishAssessmentResults(email, response, analysisResult) {
    const userType = document.querySelector('input[name="user-type"]:checked')?.value;
    const studentLevel = document.getElementById('student-level').value;

    // Check if Firebase and global variables are available
    if (typeof firebase === 'undefined') {
      throw new Error('Firebase not initialized');
    }

    if (typeof db === 'undefined') {
      throw new Error('Firestore database not initialized');
    }

    if (typeof userCompany === 'undefined' || !userCompany) {
      throw new Error('User company not set');
    }

    // Update user document with English assessment data including detailed feedback
    const companyRef = db.collection('companies').doc(userCompany);
    const userRef = companyRef.collection('users').doc(email);

    await userRef.update({
      englishProficiencyScore: analysisResult.score,
      englishProficiencyLevel: analysisResult.level,
      englishAssessmentCompleted: true,
      englishResponse: response,
      englishAssessmentTimestamp: firebase.firestore.FieldValue.serverTimestamp(),
      timeSpentOnEnglish: this.timeLimit - this.timeRemaining,
      // Store detailed feedback
      englishFeedback: analysisResult.feedback || {
        grammar: 'Assessment completed',
        vocabulary: 'Vocabulary evaluated',
        coherence: 'Structure assessed',
        overall: 'Overall proficiency evaluated'
      },
      englishStrengths: analysisResult.strengths || ['Completed the assessment'],
      englishImprovements: analysisResult.improvements || ['Continue practicing English'],
      updatedAt: firebase.firestore.FieldValue.serverTimestamp()
    });

    console.log('English assessment results stored:', {
      score: analysisResult.score,
      level: analysisResult.level,
      email: email,
      feedbackStored: !!analysisResult.feedback
    });
  }

  /**
   * Proceed to digital skills assessment for L2/GCSE students
   */
  proceedToDigitalSkills() {
    console.log('Student qualified for digital skills assessment');

    // Add a small delay to ensure all scripts are loaded and DOM is ready
    setTimeout(() => {
      try {
        // Hide English assessment container
        document.getElementById('english-assessment-container').style.display = 'none';

        // Get student information
        const email = document.getElementById('email').value.trim();
        const userType = document.querySelector('input[name="user-type"]:checked')?.value;
        const studentLevel = document.getElementById('student-level').value;
        const rawRole = `Student - ${studentLevel}`;

        console.log('Proceeding to digital skills for:', {
          email,
          userType,
          studentLevel,
          rawRole
        });

        // Check if required global functions and variables are available
        console.log('Checking global dependencies:', {
          createSkillsGapAnalyzer: typeof createSkillsGapAnalyzer,
          fetchFrameworkData: typeof window.fetchFrameworkData,
          loadQuizData: typeof loadQuizData,
          userCompany: typeof userCompany !== 'undefined' ? userCompany : 'undefined',
          db: typeof db !== 'undefined' ? 'defined' : 'undefined',
          firebase: typeof firebase !== 'undefined' ? 'defined' : 'undefined'
        });

      // Initialize framework with skeleton loading (same as normal flow)
      if (typeof createSkillsGapAnalyzer === 'function') {
        createSkillsGapAnalyzer('skills-gap-analyzer-container', {
          logoUrl: 'logosmall.png',
        });
        console.log('Skills gap analyzer created successfully');
      } else {
        console.error('createSkillsGapAnalyzer function not found');
        throw new Error('Skills gap analyzer initialization failed');
      }

      // Show framework container with skeleton loading
      const skillsContainer = document.getElementById('skills-gap-analyzer-container');
      if (skillsContainer) {
        skillsContainer.classList.remove('hidden');
        console.log('Skills gap analyzer container shown');
      } else {
        console.error('skills-gap-analyzer-container not found');
        throw new Error('Skills gap analyzer container not found');
      }

      // Add event listener for framework's proceed button (same as normal flow)
      if (!window.proceedButtonListenerAdded) {
        window.proceedButtonListenerAdded = true;
        console.log('Adding proceed button event listener');

        document.addEventListener('click', (e) => {
          if (e.target && e.target.id === 'proceed-to-assessment-btn') {
            console.log('Proceed to assessment button clicked');

            // Set intentional navigation flag
            window.intentionalNavigation = true;

            // Set the current assessment start time when starting a new assessment
            window.currentAssessmentStartTime = new Date();

            // Set the flag to indicate a new assessment has started
            window.newAssessmentStarted = true;
            console.log('New assessment started flag set to true (from English assessment proceed)');

            // Clear any existing polling when starting a new assessment
            if (window.pollInterval) {
              clearInterval(window.pollInterval);
              window.pollInterval = null;
              window.isPollingStarted = false;
            }

            document.getElementById('skills-gap-analyzer-container').classList.add('hidden');
            document.getElementById('quiz-container').style.display = 'block';

            // Reset quiz variables (same as normal flow)
            window.currentQuestion = 0;
            window.score = 0;
            window.currentSection = 1;

            // Load quiz data for student
            if (typeof loadQuizData === 'function') {
              loadQuizData();
            } else {
              console.error('loadQuizData function not found');
            }
          }
        });
      }

      // Fetch framework data using raw role (same as normal flow)
      if (typeof window.fetchFrameworkData === 'function') {
        console.log('Fetching framework data for role:', rawRole);
        window.fetchFrameworkData(rawRole).catch(error => {
          console.error('Error fetching framework data:', error);
          // Display error in content div (same as normal flow)
          const contentDiv = document.getElementById('content');
          if (contentDiv) {
            contentDiv.innerHTML = `
              <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                Failed to generate framework. Please try again.
              </div>
            `;
          }
          alert('An error occurred while preparing your assessment. Please try again.');
        });
      } else {
        console.error('fetchFrameworkData function not found');
        throw new Error('Framework data fetch function not available');
      }

      } catch (error) {
        console.error('Error in proceedToDigitalSkills:', error);
        alert('An error occurred while preparing your assessment. Please try again.');

        // Show English assessment container again if there's an error
        document.getElementById('english-assessment-container').style.display = 'flex';
      }
    }, 500); // 500ms delay to ensure all scripts are loaded
  }

  /**
   * Show success results for students who qualify for digital skills assessment
   */
  showSuccessResults(analysisResult) {
    console.log('Student qualified for digital skills assessment:', analysisResult);

    // Hide English assessment container
    document.getElementById('english-assessment-container').style.display = 'none';

    // Update success results container with detailed feedback
    this.populateResultsContainer(analysisResult, true);

    // Show success results container
    document.getElementById('english-success-container').style.display = 'flex';
  }

  /**
   * Show completion message for students below L2 level
   */
  showCompletionMessage(analysisResult) {
    console.log('Student did not qualify for digital skills assessment:', analysisResult);

    // Hide English assessment container
    document.getElementById('english-assessment-container').style.display = 'none';

    // Update completion container with detailed feedback
    this.populateResultsContainer(analysisResult, false);

    // Show completion container
    document.getElementById('english-completion-container').style.display = 'flex';
  }

  /**
   * Populate results container with detailed analysis
   */
  populateResultsContainer(analysisResult, isSuccess) {
    const containerId = isSuccess ? 'english-success-container' : 'english-completion-container';
    const container = document.getElementById(containerId);

    if (!container) {
      console.error(`Container ${containerId} not found`);
      return;
    }

    // Create detailed results HTML
    const resultsHTML = `
      <div class="results-wrapper">
        <div class="results-header">
          <h2>${isSuccess ? 'Congratulations!' : 'Assessment Complete'}</h2>
          <div class="score-display">
            <span class="score-value">${analysisResult.score}/21</span>
            <span class="score-level">${analysisResult.level} Level</span>
          </div>
        </div>
        <div class="results-content">
          <div class="feedback-section">
            <h3>Your Performance Analysis</h3>
            <div class="feedback-grid">
              <div class="feedback-item">
                <h4>Grammar</h4>
                <p>${analysisResult.feedback?.grammar || 'Assessment completed'}</p>
              </div>
              <div class="feedback-item">
                <h4>Vocabulary</h4>
                <p>${analysisResult.feedback?.vocabulary || 'Vocabulary evaluated'}</p>
              </div>
              <div class="feedback-item">
                <h4>Organization</h4>
                <p>${analysisResult.feedback?.coherence || 'Structure assessed'}</p>
              </div>
            </div>
            <div class="overall-feedback">
              <h4>Overall Assessment</h4>
              <p>${analysisResult.feedback?.overall || 'Assessment completed successfully'}</p>
            </div>
          </div>

          <div class="strengths-improvements">
            <div class="strengths-section">
              <h4>Your Strengths</h4>
              <ul>
                ${(analysisResult.strengths || ['Completed the assessment']).map(strength => `<li>${strength}</li>`).join('')}
              </ul>
            </div>
            <div class="improvements-section">
              <h4>Areas for Improvement</h4>
              <ul>
                ${(analysisResult.improvements || ['Continue practicing English']).map(improvement => `<li>${improvement}</li>`).join('')}
              </ul>
            </div>
          </div>

          <div class="next-steps">
            <h4>Next Steps</h4>
            ${isSuccess ?
              '<p>Excellent work! You have achieved the required English proficiency level (L2/GCSE) to proceed with the digital skills assessment. Click the button below to continue.</p>' :
              '<p>Thank you for completing the assessment. Based on your current level, we recommend focusing on English language development before proceeding with digital skills training. Please contact your instructor for personalized learning recommendations.</p>'
            }
          </div>

          <div class="results-actions">
            ${isSuccess ?
              '<button id="proceed-to-digital-skills" class="results-btn primary">Continue to Digital Skills Assessment</button>' :
              '<button id="return-to-portal" class="results-btn secondary">Return to Portal</button>'
            }
          </div>
        </div>
      </div>
    `;

    container.innerHTML = resultsHTML;

    // Add event listeners
    if (isSuccess) {
      const proceedBtn = document.getElementById('proceed-to-digital-skills');
      if (proceedBtn) {
        proceedBtn.addEventListener('click', () => {
          document.getElementById('english-success-container').style.display = 'none';
          this.proceedToDigitalSkills();
        });
      }
    } else {
      const returnBtn = document.getElementById('return-to-portal');
      if (returnBtn) {
        returnBtn.addEventListener('click', () => {
          window.location.href = 'https://barefootelearning.etraininglibrary.com/';
        });
      }
    }
  }

  /**
   * Clean up timer and event listeners
   */
  destroy() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  }
}

// Global instance
window.englishAssessment = null;

/**
 * Initialize English assessment for student users
 */
async function initializeEnglishAssessment() {
  if (window.englishAssessment) {
    window.englishAssessment.destroy();
  }

  window.englishAssessment = new EnglishAssessment();
  await window.englishAssessment.init();
}

/**
 * Check if user should take English assessment
 */
function shouldTakeEnglishAssessment() {
  const userType = document.querySelector('input[name="user-type"]:checked')?.value;
  return userType === 'student';
}
