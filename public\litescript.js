// Initialize Firebase
const firebaseConfig = {
  // Your Firebase configuration goes here
  apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
  authDomain: "barefoot-elearning-app.firebaseapp.com",
  projectId: "barefoot-elearning-app",
  databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
  storageBucket: "barefoot-elearning-app.appspot.com",
  messagingSenderId: "170819735788",
  appId: "1:170819735788:web:223af318437eb5d947d5c9"
};
firebase.initializeApp(firebaseConfig);
// Initialize the current question index, score, quiz data, user name, and selected option
let currentQuestion = 0;
let score = 0;
let quizData = [];
let userName = "";
let selectedOption = "";
let firebaseUniqueKey = null;
let firstName, lastName, userEmail, userRole, userPhone, userCompany;
let isConsentGiven = false;
const FEEDBACK_DELAY = 1300;
const AUTO_NEXT_DELAY = 1350;



function showConsentContainer() {
  document.getElementById("consent-container").classList.remove("hidden");
}

function hideConsentContainer() {
  document.getElementById("consent-container").classList.add("hidden");
}




// Function to load quiz data from quizData.json file
const loadQuizData = async () => {
  const res = await fetch("quizData.json");
  quizData = await res.json();
  loadQuestion();
};

// Function to load the current question and options
const loadQuestion = () => {
  const questionObj = quizData[currentQuestion];
  document.getElementById("question").innerText = questionObj.question;
  for (let i = 0; i < 4; i++) {
    const btn = document.getElementById(`btn${i}`);
    btn.innerText = questionObj.options[i];
    btn.className = "option-btn";
    btn.disabled = false;
    btn.style.opacity = 1;
    btn.style.cursor = "pointer";
  }
  document.getElementById("skip-btn").disabled = false;
  document.getElementById("skip-btn").style.opacity = 1;
  document.getElementById("skip-btn").style.cursor = "pointer";
  document.getElementById("message").innerText = "";
}

// Function to start the quiz, get the username and display the quiz container
const startQuiz = () => {
   console.log("startQuiz function called");
  document.getElementById("start-page").style.display = "none";
  document.getElementById("quiz-container").style.display = "block";
  loadQuizData();
};

const endQuiz = async () => {
  document.getElementById("quiz-container").style.display = "none";

  if (!isConsentGiven) {
    showConsentContainer();
  } else {
    document.getElementById("user-form-container").style.display = "block";
  }
};
const restartQuiz = () => {
  currentQuestion = 0;
  score = 0;
  document.getElementById("score").innerText = "0";
  document.getElementById("failure-container").style.display = "none";
  document.getElementById("quiz-container").style.display = "none";
  document.getElementById("user-form-container").style.display = "none";
  document.getElementById("result-container").style.display = "none";
  document.getElementById("consent-popup").style.display = "none";
  document.getElementById("start-page").style.display = "block";
};

const showResults = () => {
  const totalQuestions = quizData.length;
  const passThreshold = 0.7 * totalQuestions;
  const scorePercentage = (score / totalQuestions) * 100;

  const resultText = document.getElementById("result-text");

  if (scorePercentage < 30) {
    resultText.innerHTML = `Thank you for taking our initial assessment, ${firstName}.<br>You scored <strong>${scorePercentage.toFixed(0)}%</strong> of the questions right.`;
    remarks.innerText = "REMARKS: Low knowledge";
    comments.innerText = "Learning is key.";
  } else if (scorePercentage < 50) {
    resultText.innerHTML = `Thank you for taking our initial assessment, ${firstName}.<br>You scored <strong>${scorePercentage.toFixed(0)}%</strong> of the questions right.`;
    remarks.innerText = "REMARKS: Fair knowledge.";
    comments.innerText = "You're making progress, but there's room for growth.";
  } else if (scorePercentage >= 50 && scorePercentage < 70) {
    resultText.innerHTML = `Thank you for taking our initial assessment, ${firstName}.<br>You scored <strong>${scorePercentage.toFixed(0)}%</strong> of the questions right.`;
    remarks.innerText = "REMARKS: Good knowledge.";
    comments.innerText = "Well done! You have a solid understanding. Continue building on this foundation.";
  } else {
    resultText.innerHTML = `Thank you for taking our initial assessment, ${firstName}.<br>You scored <strong>${scorePercentage.toFixed(0)}%</strong> of the questions right.`;
    remarks.innerText = "REMARKS: Excellent knowledge.";
    comments.innerText = "Its time to learn some new skills.";
  }
};

// Event listeners for Start Quiz button and Next Question button
document.getElementById("start-btn").addEventListener("click", () => {
  const termsCheckbox = document.getElementById("terms-checkbox");
  // Check if the checkbox is marked
  if (termsCheckbox.checked) {
    startQuiz(); // Call the startQuiz function if the checkbox is marked
  } else {
    alert("Please agree to the Privacy Policy before starting the quiz.");
  }
});
document.getElementById("next-btn").addEventListener("click", () => {
  currentQuestion++;
  if (currentQuestion < quizData.length) {
    loadQuestion();
    const progress = (currentQuestion / quizData.length) * 100;
    document.getElementById("progress-bar-fill").style.width = `${progress}%`;
    document.getElementById("progress-bar-text").innerText = `${Math.round(progress)}%`;
  } else {
    endQuiz();
  }
});

function showConsentContainer() {
  if (!isConsentGiven) {
    document.getElementById("consent-container").classList.remove("hidden");
  }
}

document.getElementById("close-consent-btn").addEventListener("click", () => {
  const consentCheckbox = document.getElementById("consent-checkbox");
  if (!consentCheckbox.checked) {
    const confirmResult = confirm("To ensure your privacy is protected, as required by GDPR, please agree to our Privacy Policy before proceeding with the assessment. If you'd prefer not to proceed, click 'OK' to restart the assessment.");
    if (confirmResult) {
      const quitConfirmation = confirm("Are you sure you want to quit the assessment? Pressing OK will restart the app and erase all your progress.");
      if (quitConfirmation) {
        window.location.reload();
      }
    }
    const proceedConfirmation = confirm("You have agreed to the Privacy Policy. Click 'OK' to proceed with the assessment or 'Cancel' to uncheck the consent checkbox.");
    if (proceedConfirmation) {
      // User chose to proceed
      hideConsentContainer();
      document.getElementById("user-form-container").style.display = "block";
    } else {
      // User chose to uncheck the consent checkbox
      consentCheckbox.checked = false;
    }
  }
});
// Handle the case when the user doesn't provide consent
function handleNoConsent() {
}


document.getElementById("consent-btn").addEventListener("click", () => {
  const consentCheckbox = document.getElementById("consent-checkbox");
  if (consentCheckbox.checked) {
    isConsentGiven = true; // Set the flag to true
    hideConsentContainer();
    document.getElementById("user-form-container").style.display = "block";
  } else {
    alert("Please agree to the Privacy Policy before proceeding.");
  }
});

const userForm = document.getElementById("user-form");
userForm.addEventListener("submit", async (event) => {
  event.preventDefault();
  // Show the loading overlay
  document.getElementById("loading-overlay").style.display = "flex";

  // Get the user data
  firstName = document.getElementById("first-name").value.trim();
  lastName = document.getElementById("last-name").value.trim();
  userEmail = document.getElementById("email").value.trim();
  userRole = document.getElementById("role").value;
  userCompany = document.getElementById("company").value;
  userPhone = document.getElementById("phone").value;

  if (firstName && lastName && userEmail && userRole && userCompany && userPhone) {
    const userData = { firstName, lastName, userEmail, userRole, userCompany, userPhone, "lite assesment score": score };
    try {
      // Store the user data in the Firebase Realtime Database
      const userRef = firebase.database().ref('users');
      const newUserRef = userRef.push();
      firebaseUniqueKey = newUserRef.key; // Get the unique key generated by Firebase and store it in the global variable
      await newUserRef.set({ ...userData, key: firebaseUniqueKey }); // Store the unique key along with user data
      console.log('User data saved to Firebase:', userData);
      document.getElementById("loading-overlay").style.display = "none";

      // Display the result container
      document.getElementById("user-form-container").style.display = "none";
      document.getElementById("result-container").style.display = "block";
      showResults();
    } catch (error) {
      console.error('Error saving user data to Firebase:', error);
      alert('An error occurred while submitting user data');
    }
  } else {
    alert("Please fill in all the fields.");
  }
});
const tryItLink = document.getElementById("try-it-link");
tryItLink.addEventListener("click", async (event) => {
  event.preventDefault();

  if (firebaseUniqueKey) {
    // Show the loading overlay
    document.getElementById("loading-overlay").style.display = "flex";
    try {
      const response = await fetch('/receive-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ key: firebaseUniqueKey })
      });

      if (response.ok) {
        console.log('Key sent to server successfully');
        // Open index2.html in a new tab or window
        window.location.assign('index2.html');
      } else {
        const errorData = await response.json();
        console.error('Error from server:', errorData.error);
        alert(`An error occurred: ${errorData.error}`);
      }
      // Hide the loading overlay after the key is sent
      document.getElementById("loading-overlay").style.display = "none";
    } catch (error) {
      console.error('Error sending key to server:', error);
      alert('An error occurred while sending the key to the server');
    }
  } else {
    console.error('Firebase unique key not available');
    alert('Firebase unique key is not available');
  }
});
// function for the book a call button
function openBookingPage() {
  window.open("https://barefootelearning.com/book-a-call/", "_blank");
}
// Event listeners for the option buttons, updating score and showing whether the answer is correct or not
for (let i = 0; i < 4; i++) {
  document.getElementById(`btn${i}`).addEventListener("click", (event) => {
    selectedOption = event.target;
    if (quizData[currentQuestion].options[i] === quizData[currentQuestion].answer) {
      score++;
      document.getElementById("score").innerText = score;
      // Use neutral styling instead of 'correct' class
      selectedOption.className = "option-btn";
      // Show neutral feedback message
      document.getElementById("message").innerHTML = '<span class="response-recorded">Response recorded</span>';
    } else {
      // Use neutral styling instead of 'wrong' class
      selectedOption.className = "option-btn";
      // Show neutral feedback message
      document.getElementById("message").innerHTML = '<span class="response-recorded">Response recorded</span>';
    }
    for (let j = 0; j < 4; j++) {
      document.getElementById(`btn${j}`).disabled = true;
      document.getElementById(`btn${j}`).style.cursor = "not-allowed";
      document.getElementById(`btn${j}`).style.opacity = 0.5;
    }
    selectedOption.style.opacity = 1;

    // Disable the "Skip" button
    document.getElementById("skip-btn").disabled = true;
    document.getElementById("skip-btn").style.cursor = "not-allowed";
    document.getElementById("skip-btn").style.opacity = 0.5;

    // Automatically move to the next question after delays
    setTimeout(() => {
      // This timeout is for showing feedback
      setTimeout(() => {
        // This timeout is for moving to the next question
        currentQuestion++;
        if (currentQuestion < quizData.length) {
          loadQuestion();
          const progress = (currentQuestion / quizData.length) * 100;
          document.getElementById("progress-bar-fill").style.width = `${progress}%`;
          document.getElementById("progress-bar-text").innerText = `${Math.round(progress)}%`;
        } else {
          endQuiz();
        }
      }, AUTO_NEXT_DELAY - FEEDBACK_DELAY);
    }, FEEDBACK_DELAY);
  });
}

document.getElementById("skip-btn").addEventListener("click", () => {
  currentQuestion++;
  if (currentQuestion < quizData.length) {
    loadQuestion();
    const progress = (currentQuestion / quizData.length) * 100;
    document.getElementById("progress-bar-fill").style.width = `${progress}%`;
    document.getElementById("progress-bar-text").innerText = `${Math.round(progress)}%`;
  } else {
    endQuiz();
  }
});