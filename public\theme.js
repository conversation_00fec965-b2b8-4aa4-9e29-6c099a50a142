// Official color palette
const THEME = {
  MAIN_BLUE: '#1547bb',
  DARK_BLUE: '#121c41',
  GREEN: '#7ae582',
  
  // Other UI colors
  ERROR_RED: '#e74c3c',
  BACKGROUND_LIGHT: 'rgba(255, 255, 255, 0.9)',
  TEXT_DARK: '#333333',
  TEXT_LIGHT: '#666666'
};

// Export to make accessible to other scripts
window.THEME = THEME;

document.addEventListener('DOMContentLoaded', function() {
  // Add responsive viewport meta tag if not already present
  if (!document.querySelector('meta[name="viewport"]')) {
    const metaViewport = document.createElement('meta');
    metaViewport.name = 'viewport';
    metaViewport.content = 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no';
    document.head.appendChild(metaViewport);
  }

  // Improve mobile interaction for role suggestions
  improveMobileRoleSuggestions();

  // Add touch-friendly behavior to buttons
  addTouchFriendlyBehavior();

  // Ensure quiz container remains scrollable on mobile
  improveQuizScrolling();
});

function improveMobileRoleSuggestions() {
  const roleInput = document.getElementById('role');
  if (!roleInput) return;

  // Create a better container for role suggestions on mobile
  const suggestionsContainer = document.querySelector('.role-suggestions');
  if (suggestionsContainer) {
    // Add a touch-friendly behavior for mobile
    suggestionsContainer.addEventListener('touchstart', function(e) {
      // Prevent immediate closing on mobile touch start
      e.stopPropagation();
    });

    // Close suggestions when tapping elsewhere on mobile
    document.addEventListener('touchstart', function(e) {
      if (suggestionsContainer.style.display !== 'none' && 
          !roleInput.contains(e.target) && 
          !suggestionsContainer.contains(e.target)) {
        suggestionsContainer.style.display = 'none';
      }
    });

    // Ensure that on mobile the suggestions don't get clipped
    window.addEventListener('resize', function() {
      if (window.innerWidth <= 768) {
        if (suggestionsContainer.style.display !== 'none') {
          // Calculate if we need to adjust position to remain visible
          const inputRect = roleInput.getBoundingClientRect();
          const availableHeight = window.innerHeight - inputRect.bottom - 20;
          
          if (availableHeight < 150) {
            suggestionsContainer.style.maxHeight = `${availableHeight}px`;
          } else {
            suggestionsContainer.style.maxHeight = '150px';
          }
        }
      }
    });
  }
}

function addTouchFriendlyBehavior() {
  // Make quiz buttons more responsive on touch
  const buttons = document.querySelectorAll('.option-btn, #next-btn, .skip-btn');
  
  buttons.forEach(button => {
    // Add active state for touch
    button.addEventListener('touchstart', function() {
      this.classList.add('touch-active');
    });
    
    button.addEventListener('touchend', function() {
      this.classList.remove('touch-active');
    });
  });
}

function improveQuizScrolling() {
  // Ensure quiz container is scrollable on mobile
  const quizContainer = document.getElementById('quiz-container');
  if (quizContainer) {
    quizContainer.style.overflowY = 'auto';
    quizContainer.style.webkitOverflowScrolling = 'touch';
    
    // Adjust height on mobile to ensure it fits in viewport
    if (window.innerWidth <= 768) {
      quizContainer.style.maxHeight = '80vh';
    }
  }
}
