(function(global) {
    // Configuration and state
    let isModalInitialized = false;
    let isClosing = false;
    let currentChartInstance = null;
    let currentData = null;

    async function showSkillsGapAnalysis(data = null) {
        try {
            if (data) {
                currentData = data;  // Store the data
            }

            if (isModalInitialized) {
                await resetAndShowModal();
                return;
            }

            isClosing = false;

            // Create modal structure
            const overlay = document.createElement('div');
            overlay.id = 'skills-gap-overlay';
            overlay.className = 'modal-overlay';

            if (currentData) {
                overlay.innerHTML = createModalHTML(currentData);
            } else {
                throw new Error('Invalid data structure for modal creation');
            }

            document.body.appendChild(overlay);
            isModalInitialized = true;

            // Initialize event listeners
            initializeEventListeners(overlay);

            // Create radar chart with actual data
            await createRadarChart(currentData);

            // Animate modal appearance
            requestAnimationFrame(() => {
                if (!isClosing) {
                    overlay.style.opacity = '1';
                    const modalContent = overlay.querySelector('.modal-content');
                    if (modalContent) {
                        modalContent.style.opacity = '1';
                        modalContent.style.transform = 'scale(1)';
                    }
                }
            });

            addStyles();
        } catch (error) {
            console.error('Error showing skills gap modal:', error);
            throw error;
        }
    }

    async function resetAndShowModal() {
        const overlay = document.getElementById('skills-gap-overlay');
        if (!overlay) return;

        // Clear existing chart container
        const chartContainer = overlay.querySelector('#radar-chart');
        if (chartContainer) {
            chartContainer.innerHTML = '';
        }

        // Recreate chart with updated data
        await createRadarChart(currentData);

        // Show modal
        overlay.style.display = 'flex';
        requestAnimationFrame(() => {
            overlay.style.opacity = '1';
            const modalContent = overlay.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.opacity = '1';
                modalContent.style.transform = 'scale(1)';
            }
        });
    }

    function createCompetencyCard(competency, data) {
        // Check if we have any strength areas
        const hasStrengths = Array.isArray(data.strengthAreas) && data.strengthAreas.length > 0;

        // Check if we have any gap areas
        const hasGaps = Array.isArray(data.gapAreas) && data.gapAreas.length > 0;

        // Create the strength areas HTML if we have any
        const strengthAreasHTML = hasStrengths
            ? `
                <div class="strength-areas">
                    <h4>Strengths</h4>
                    ${data.strengthAreas.map(area => `
                        <span class="badge strength">${area}</span>
                    `).join('')}
                </div>
            `
            : '';

        // Create the gap areas HTML if we have any
        const gapAreasHTML = hasGaps
            ? `
                <div class="gap-areas">
                    <h4>Gaps</h4>
                    ${data.gapAreas.map(area => `
                        <span class="badge gap">${area}</span>
                    `).join('')}
                </div>
            `
            : '';

        // Create the no data message if we don't have any strengths or gaps
        const noDataHTML = !hasStrengths && !hasGaps
            ? `<div class="no-data">No data available</div>`
            : '';

        return `
            <div class="competency-card">
                <h3>${competency}</h3>
                <div class="proficiency-meter">
                    <div class="progress-bar">
                        <div class="progress" style="width: ${data.proficiencyLevel}"></div>
                    </div>
                    <span class="proficiency-level">${data.proficiencyLevel}</span>
                </div>
                <div class="areas-section">
                    ${strengthAreasHTML}
                    ${gapAreasHTML}
                    ${noDataHTML}
                </div>
            </div>
        `;
    }

    async function createRadarChart(data = null) {
        try {
            if (!data || !data.report || !data.report.competencyAnalysis) {
                throw new Error('Invalid data for radar chart creation');
            }

            // Cleanup existing chart instance if any
            if (currentChartInstance) {
                currentChartInstance.destroy();
                currentChartInstance = null;
            }

            const chartData = Object.entries(data.report.competencyAnalysis).map(([name, compData]) => ({
                axis: name,
                value: parseInt(compData.proficiencyLevel) || 0
            }));

            // Load Chart.js if not already present
            if (!window.Chart) {
                await new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js';
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            }

            const container = document.querySelector('#radar-chart');
            if (!container) {
                throw new Error('Radar chart container not found');
            }

            // Prepare container
            container.innerHTML = '';
            const ctx = document.createElement('canvas');
            container.appendChild(ctx);

            // Instantiate chart
            currentChartInstance = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: chartData.map(d => d.axis),
                    datasets: [{
                        data: chartData.map(d => d.value),
                        backgroundColor: 'rgba(30, 58, 138, 0.1)',
                        borderColor: '#1e3a8a',
                        pointBackgroundColor: '#1e3a8a'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: { display: false },
                            grid: { color: '#d1d5db' },
                            angleLines: { color: '#d1d5db' }
                        }
                    },
                    plugins: {
                        legend: { display: false }
                    }
                }
            });
        } catch (error) {
            console.error('Error creating radar chart:', error);
            throw error;
        }
    }

    function createModalHTML(data) {
        const competencyCards = data
            ? Object.entries(data.report.competencyAnalysis)
                  .map(([competency, competencyData]) =>
                      createCompetencyCard(competency, competencyData)
                  )
                  .join('')
            : '';

        // Core (primary) recommendations
        const coreRecommendations = data.recommendations.map(rec => `
            <li>
                <strong>${rec.course}</strong>
                <p>${rec.reason}</p>
            </li>
        `).join('');

        // Additional (collapsible) recommendations
        const crossPathSection = data.other_learning_paths_courses && data.other_learning_paths_courses.length > 0
            ? `
                <div class="recommendations-section cross-path-recommendations">
                    <div class="cross-path-header">
                        <h3>Additional Learning Path Recommendations</h3>
                        <button id="toggle-cross-paths-btn" class="toggle-cross-paths-btn">Show/Hide</button>
                    </div>
                    <div id="cross-paths-collapsible" class="cross-paths-collapsible">
                        <ul>
                            ${
                                data.other_learning_paths_courses.map(rec => `
                                    <li>
                                        <strong>${rec.course}</strong>
                                        <p>${rec.reason}</p>
                                    </li>
                                `).join('')
                            }
                        </ul>
                    </div>
                </div>
            `
            : '';

        return `
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title-container">
                        <h2 class="modal-employee-title">
                            ${data && data.report ? `${data.report.employeeName || ''} ${data.report.role ? `- ${data.report.role}` : ''}` : ''}
                        </h2>
                        <h3 class="modal-subtitle">Skills Gap Analysis Report</h3>
                    </div>
                    <div class="modal-actions">
                        <button id="close-skills-modal" class="close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="modal-body">
                    <div id="radar-chart" class="chart-container"></div>

                    <div class="competency-grid">
                        ${competencyCards}
                    </div>

                    <div class="learning-path-section">
                        <h3>Learning Path Recommendations</h3>
                        <p>${data ? data.report.summary : ''}</p>
                    </div>

                    <div class="recommendations-container">
                        <div class="recommendations-section primary-recommendations">
                            <h3>Core Learning Path Recommendations</h3>
                            <ul>
                                ${coreRecommendations}
                            </ul>
                        </div>
                        ${crossPathSection}
                    </div>
                </div>
            </div>
        `;
    }

    function addStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            /***** OVERLAY *****/
            .modal-overlay {
                position: fixed;
                top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0, 0, 0, 0.4);
                display: flex;
                justify-content: center;
                align-items: center;
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 1000;
            }

            /***** MODAL CONTAINER *****/
            .modal-content {
                background: #ffffff;
                border-radius: 6px;
                width: 90%;
                max-width: 900px;
                max-height: 90vh;
                overflow-y: auto;
                opacity: 0;
                transform: scale(0.95);
                transition: all 0.3s ease;
                font-family: sans-serif;
                color: #374151;
                position: relative;
                /* Slightly smaller base font for a minimal look */
                font-size: 0.875rem;
            }

            /***** HEADER *****/
            .modal-header {
                padding: 1rem;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }

            .modal-title-container {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }

            .modal-employee-title {
                font-size: 1rem; /* reduced from 1.2rem */
                font-weight: 600;
                color: #1e3a8a;
                margin: 0;
            }

            .modal-subtitle {
                font-size: 0.8rem; /* reduced from 0.9rem */
                font-weight: 500;
                color: #4b5563;
                margin: 0;
            }

            /***** ACTIONS *****/
            .modal-actions {
                display: flex;
                gap: 0.5rem;
            }

            .close-modal-button {
                background: none;
                border: 1px solid #1e3a8a;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                cursor: pointer;
                color: #1e3a8a;
            }

            .close-modal-button svg {
                stroke: #1e3a8a;
            }

            .close-modal-button:hover {
                background: #1e3a8a;
                color: #fff;
            }

            /***** BODY *****/
            .modal-body {
                padding: 1rem;
                line-height: 1.4;
            }

            .chart-container {
                position: relative;
                height: 300px;
                width: 80%;  /* center by setting width and margin auto */
                margin: 0 auto 1.5rem;
            }

            /***** COMPETENCY GRID *****/
            .competency-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.75rem;
                margin-bottom: 1.5rem;
                padding: 0 1rem;
            }

            .competency-card {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .competency-card h3 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem; /* reduced from 1rem */
                color: #1e3a8a;
            }

            /***** PROFICIENCY METER *****/
            .proficiency-meter {
                margin: 0.75rem 0;
                display: flex;
                align-items: center;
            }

            .progress-bar {
                background: #e5e7eb;
                border-radius: 9999px;
                height: 6px;
                width: 100%;
                margin-right: 0.5rem;
                overflow: hidden;
            }

            .progress {
                background: #1547bb; /* Changed from #f59e0b (orange) to main blue */
                border-radius: 9999px;
                height: 100%;
                transition: width 0.3s ease;
            }

            .proficiency-level {
                font-size: 0.75rem;
                color: #6b7280;
            }

            /***** STRENGTH & GAP AREAS *****/
            .areas-section h4 {
                margin: 0.5rem 0 0.25rem;
                font-size: 0.8rem;
                font-weight: 600;
                color: #374151;
            }

            .badge {
                display: inline-block;
                padding: 0.25rem 0.5rem;
                border-radius: 9999px;
                font-size: 0.75rem;
                margin: 0.25rem 0.25rem 0 0;
            }

            .badge.strength {
                background: #f0f9ff; /* lighter than #e0f2fe */
                color: #1e3a8a;
            }

            .badge.gap {
                background: #fef3f2; /* lighter red background */
                color: #9b1c1c;
            }

            .no-data {
                font-size: 0.8rem;
                color: #6b7280;
                font-style: italic;
                padding: 0.5rem 0;
                text-align: center;
            }

            /***** LEARNING PATH *****/
            .learning-path-section {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f9fafb;
                margin: 0 1rem 1.5rem;
            }

            .learning-path-section h3 {
                margin-top: 0;
                font-size: 0.9rem; /* reduced from 1rem */
                color: #1e3a8a;
                margin-bottom: 0.5rem;
                font-weight: 600;
            }

            /***** RECOMMENDATIONS *****/
            .recommendations-container {
                display: flex;
                flex-direction: column;
                gap: 1rem;
                padding: 0 1rem;
            }

            .recommendations-section {
                background: #f9fafb;
                border-radius: 6px;
                padding: 1rem;
                border-left: 4px solid transparent;
            }

            .recommendations-section ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .recommendations-section li {
                border-bottom: 1px solid #e5e7eb;
                padding: 0.5rem 0;
            }

            .recommendations-section li:last-child {
                border-bottom: none;
            }

            .primary-recommendations {
                border-left-color: #1e3a8a;
            }

            .cross-path-recommendations {
                border-left-color: #059669;
            }

            .recommendations-section h3 {
                margin-top: 0;
                margin-bottom: 0.75rem;
                font-weight: 600;
                font-size: 0.9rem;
                color: #374151;
            }

            /***** COLLAPSIBLE CROSS-PATH *****/
            .cross-path-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.25rem;
            }

            .toggle-cross-paths-btn {
                background: none;
                border: 1px solid #059669;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                font-size: 0.8rem;
                cursor: pointer;
                color: #059669;
            }

            .toggle-cross-paths-btn:hover {
                background: #059669;
                color: #fff;
            }

            .cross-paths-collapsible {
                max-height: 0;
                opacity: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
            }

            .cross-paths-collapsible.expanded {
                max-height: 1000px; /* Adjust based on expected content height */
                opacity: 1;
            }
        `;
        document.head.appendChild(styleSheet);
    }

    function initializeEventListeners(overlay) {
        // Clean up old listeners if any
        const existingOverlay = document.getElementById('skills-gap-overlay');
        if (existingOverlay) {
            const oldCloseButton = existingOverlay.querySelector('#close-skills-modal');
            if (oldCloseButton) {
                oldCloseButton.removeEventListener('click', hideModal);
            }
            existingOverlay.removeEventListener('click', overlayClickHandler);
        }

        // Close button
        const closeButton = overlay.querySelector('#close-skills-modal');
        if (closeButton) {
            closeButton.addEventListener('click', hideModal);
        }

        // Overlay click to close
        overlay.addEventListener('click', overlayClickHandler);

        // Collapsible cross-path recommendations
        const toggleButton = overlay.querySelector('#toggle-cross-paths-btn');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                const collapsible = overlay.querySelector('#cross-paths-collapsible');
                if (collapsible) {
                    collapsible.classList.toggle('expanded');
                }
            });
        }
    }

    function overlayClickHandler(event) {
        // Close modal if user clicks outside content
        if (event.target.id === 'skills-gap-overlay') {
            hideModal();
        }
    }

    async function hideModal() {
        isClosing = true;
        const overlay = document.getElementById('skills-gap-overlay');
        if (!overlay) return;

        overlay.style.opacity = '0';
        const modalContent = overlay.querySelector('.modal-content');
        if (modalContent) {
            modalContent.style.opacity = '0';
            modalContent.style.transform = 'scale(0.95)';
        }

        // Clean up chart instance
        if (currentChartInstance) {
            currentChartInstance.destroy();
            currentChartInstance = null;
        }

        await new Promise(resolve => setTimeout(resolve, 300));
        overlay.style.display = 'none';
    }

    function showModal() {
        const overlay = document.getElementById('skills-gap-overlay');
        if (overlay) {
            overlay.style.display = 'flex';
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }
            }, 10);
        }
    }

    // Public API
    global.showSkillsGapAnalysis = showSkillsGapAnalysis;

})(typeof window !== 'undefined' ? window : global);
