// Initialize Firebase
const firebaseConfig = {
  // Your Firebase configuration goes here
  apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
  authDomain: "barefoot-elearning-app.firebaseapp.com",
  projectId: "barefoot-elearning-app",
  databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
  storageBucket: "barefoot-elearning-app.appspot.com",
  messagingSenderId: "170819735788",
  appId: "1:170819735788:web:223af318437eb5d947d5c9"
};

let app;
const initializeFirebase = () => {
  app = firebase.initializeApp(firebaseConfig);
};

window.onload = () => {
  initializeFirebase();

   // DOM manipulation and event listeners here
   const startBtn = document.getElementById("start-btn");
   if (startBtn) {
     startBtn.addEventListener("click", startQuiz);
   }
};

// Initialize the current question index, score, quiz data, user name, and selected option
const sectionNames = ["Essentials", "Intermediate", "Advanced", "Champions"];
let currentQuestion = 0;
let score = 0;
let quizData = [];
let currentSection = 1;
let userName = "";
let selectedOption = "";
let userKey = "";
const FEEDBACK_DELAY = 1300;
const AUTO_NEXT_DELAY = 1350;
const pathwayCourses = {
  "Essentials": [
    "Word Essentials", "Excel Essentials", "Outlook Essentials", "Power Point Essentials", "Teams 101", "Teams Essentials", "SharePoint Essentials", "Cyber Security – Awareness Fundamentals", "Cyber Security – Holistic Security for End-Users", "EDS Entry", "EDS Level 1"
  ],
  "Intermediate": [
    "Word Power Users", "Excel Power Users", "Outlook Power Users", "Power Point Power Users", "Microsoft Teams", "SharePoint Power Users", "Cyber Security – End-User Vigilance", "Cyber Security – Business Protection Cyber Security", "Cybersecurity: Protecting Individuals Protects Your Organization"
  ],
  "Advanced": [
    "Word Pro", "Excel Pro", "Outlook Pro", "Power Point Pro", "Teams Mobile", "Power BI", "Planner", "SharePoint Pro", "Cybersecurity - Spotlight: Security Awareness Starring \"Kevin\" by Lucy", "Cybersecurity for Small Businesses", "Cyber Security – Business Protection Cyber Security for IT"
  ],
  "Champions": [
    "Artificial intelligence", "One Drive", "OneNote", "New Teams - Accessing and Navigating Teams", "New Teams - Utilising Teams Channels", "New Teams - Mastering Teams Chat", "New Teams - Managing Your Teams Experience", "New Teams - Mastering Copilot and Apps", "New Teams -Teams Meetings", "New Teams -Mastering Teams Meetings", "PowerApps", "Model-Driven PowerApps", "Attack Simulator", "Microsoft Defender"
  ]
};
// Function to load quiz data from quizData.json file
const loadQuizData = async () => {
  const quizDataFile = `quizData${currentSection}.json`;
  const res = await fetch(quizDataFile);
  quizData = await res.json();
  loadQuestion();
  document.getElementById("current-section").innerText = currentSection;
  document.getElementById("section-name").innerText = sectionNames[currentSection - 1];
  document.getElementById("progress-bar-fill").style.width = "0%";
  document.getElementById("progress-bar-text").innerText = "0%";
};
// Function to load the current question and options
const loadQuestion = () => {
  const questionObj = quizData[currentQuestion];
  document.getElementById("question").innerText = questionObj.question;
  for (let i = 0; i < 4; i++) {
    const btn = document.getElementById(`btn${i}`);
    btn.innerText = questionObj.options[i];
    btn.className = "option-btn";
    btn.disabled = false;
    btn.style.opacity = 1;
    btn.style.cursor = "pointer";
  }
  document.getElementById("skip-btn").disabled = false;
  document.getElementById("skip-btn").style.opacity = 1;
  document.getElementById("skip-btn").style.cursor = "pointer";
  document.getElementById("message").innerText = "";
}

const startQuiz = () => {
  const enteredKey = document.getElementById("key-input").value.trim();
  const keyErrorElement = document.getElementById("key-error");

  if (enteredKey) {
    keyErrorElement.textContent = ""; // Clear any previous error message
    validateUserKey(enteredKey);
  } else {
    keyErrorElement.textContent = "Please enter your key.";
    document.getElementById("key-input").classList.add("shake");
    setTimeout(() => {
      document.getElementById("key-input").classList.remove("shake");
    }, 500); // Remove the shake animation after 500ms
  }
};

const validateUserKey = async (enteredKey) => {
  try {
    // Show the loading overlay
    document.getElementById("loading-overlay").style.display = "flex";

    const userRef = firebase.database().ref(`users/${enteredKey}`);
    const snapshot = await userRef.once("value");
    if (snapshot.exists()) {
      const userData = snapshot.val();
      userName = `${userData.firstName} ${userData.lastName}`;
      userEmail = userData.userEmail;
      userRole = userData.userRole;
      userKey = enteredKey;
      document.getElementById("start-page").style.display = "none";
      document.getElementById("quiz-container").style.display = "block";
      loadQuizData();
    } else {
      document.getElementById("key-error").textContent = "Invalid key. Please check and try again.";
      document.getElementById("key-input").classList.add("shake");
      setTimeout(() => {
        document.getElementById("key-input").classList.remove("shake");
      }, 500);
    }
  } catch (error) {
    console.error("Error validating user key:", error);
    document.getElementById("key-error").textContent = "An error occurred while validating the key.";
    document.getElementById("key-input").classList.add("shake");
    setTimeout(() => {
      document.getElementById("key-input").classList.remove("shake");
    }, 500);
  } finally {
    // Hide the loading overlay
    document.getElementById("loading-overlay").style.display = "none";
  }
};

const endQuiz = async () => {
  document.getElementById("quiz-container").style.display = "none";
  const totalQuestions = quizData.length;
  const passThreshold = 0.7 * totalQuestions; // For example, 70% to pass

  if (score >= passThreshold) {
    if (currentSection === 4) {
      document.getElementById("final-success-container").style.display = "block";
    } else {
      document.getElementById("success-container").style.display = "block";
      document.getElementById("current-section").innerText = currentSection;
      const successHeading = document.getElementById("success-heading");
      successHeading.innerText = `You passed Section ${currentSection}: ${sectionNames[currentSection - 1]}`;
    }
  } else {
    document.getElementById("failure-container").style.display = "block";
    const recommendationText = document.getElementById("recommendation-text");
    const pathwayBtn = document.getElementById("pathway-btn");
    const sectionName = sectionNames[currentSection - 1];
    const suggestedPathway = document.getElementById("suggested-pathway");
    const courseList = document.getElementById("pathway-container").querySelector(".course-list");

    pathwayBtn.onclick = () => {
      document.getElementById("pathway-container").style.display = "block";
      document.getElementById("failure-container").style.display = "none";
      suggestedPathway.textContent = sectionName;
      courseList.innerHTML = "";
      pathwayCourses[sectionName].forEach(course => {
        const li = document.createElement("li");
        li.textContent = course;
        courseList.appendChild(li);
      });
    };

    if (currentSection === 1) {
      recommendationText.innerHTML = "Based on our analysis, we suggest the <strong>Essentials pathway</strong> as the most suitable option to meet your requirements.";
    } else if (currentSection === 2) {
      recommendationText.innerHTML = "Our evaluation indicates that the <strong>Intermediate pathway</strong> would be the most appropriate choice for you.";
    } else if (currentSection === 3) {
      recommendationText.innerHTML = "Our evaluation indicates the <strong>Advanced pathway</strong> is the most appropriate option to challenge your existing skills.";
    } else if (currentSection === 4) {
      recommendationText.innerHTML = "Our evaluation indicates the <strong>Champions pathway</strong> is the most appropriate option to challenge your existing skills.";
    }

    try {
      const userRef = firebase.database().ref(`users/${userKey}`);
      const userSnapshot = await userRef.once("value");
      const userData = userSnapshot.val();

      const resultData = {
        email: userData.userEmail,
        section: sectionNames[currentSection - 1],
        score: score,
        role: userData.userRole,
        totalQuestions: quizData.length,
        key: userKey,
        isNewUser: true,
        timestamp: new Date().getTime(),
        firstName: userData.firstName,
        lastName: userData.lastName,
        userCompany: userData.userCompany,
        userPhone: userData.userPhone,
      };

      // Store the assessment result in the Firebase Realtime Database
      const resultsRef = firebase.database().ref('results');
      const newResultRef = resultsRef.push();
      await newResultRef.set(resultData);
      console.log('Assessment result saved to Firebase:', resultData);
    } catch (error) {
      console.error('Error saving assessment result to Firebase:', error);
      alert('An error occurred while submitting assessment result');
    }
  }
};
const restartQuiz = () => {
  currentQuestion = 0;
  score = 0;
  document.getElementById("score").innerText = "0";
  document.getElementById("failure-container").style.display = "none";
  document.getElementById("start-page").style.display = "block";
  document.getElementById("progress-bar-fill").style.width = "0%";
  document.getElementById("progress-bar-text").innerText = "0%";
  loadQuizData();
};
document.getElementById("restart-btn").addEventListener("click", restartQuiz);
// Event listeners for Start Quiz button and Next Question button
document.getElementById("start-btn").addEventListener("click", () => {
  startQuiz(); // Call the startQuiz function
});
document.getElementById("next-btn").addEventListener("click", () => {
  currentQuestion++;
  if (currentQuestion < quizData.length) {
    loadQuestion();
    const progress = (currentQuestion / quizData.length) * 100;
    document.getElementById("progress-bar-fill").style.width = `${progress}%`;
    document.getElementById("progress-bar-text").innerText = `${Math.round(progress)}%`;
  } else {
    endQuiz();
  }
});

for (let i = 0; i < 4; i++) {
  document.getElementById(`btn${i}`).addEventListener("click", (event) => {
    selectedOption = event.target;
    if (quizData[currentQuestion].options[i] === quizData[currentQuestion].answer) {
      score++;
      document.getElementById("score").innerText = score;
      // Use neutral styling instead of 'correct' class
      selectedOption.className = "option-btn";
      // Show neutral feedback message
      document.getElementById("message").innerHTML = '<span class="response-recorded">Response recorded</span>';
    } else {
      // Use neutral styling instead of 'wrong' class
      selectedOption.className = "option-btn";
      // Show neutral feedback message
      document.getElementById("message").innerHTML = '<span class="response-recorded">Response recorded</span>';
    }
    for (let j = 0; j < 4; j++) {
      document.getElementById(`btn${j}`).disabled = true;
      document.getElementById(`btn${j}`).style.cursor = "not-allowed";
      document.getElementById(`btn${j}`).style.opacity = 0.5;
    }
    selectedOption.style.opacity = 1;

    // Disable the "Skip" button
    document.getElementById("skip-btn").disabled = true;
    document.getElementById("skip-btn").style.cursor = "not-allowed";
    document.getElementById("skip-btn").style.opacity = 0.5;

    // Automatically move to the next question after delays
    setTimeout(() => {
      // This timeout is for showing feedback
      setTimeout(() => {
        // This timeout is for moving to the next question
        currentQuestion++;
        if (currentQuestion < quizData.length) {
          loadQuestion();
          const progress = (currentQuestion / quizData.length) * 100;
          document.getElementById("progress-bar-fill").style.width = `${progress}%`;
          document.getElementById("progress-bar-text").innerText = `${Math.round(progress)}%`;
        } else {
          endQuiz();
        }
      }, AUTO_NEXT_DELAY - FEEDBACK_DELAY);
    }, FEEDBACK_DELAY);
  });
}

// Event Listener for the "Next Section" button
document.getElementById("next-section-btn").addEventListener("click", () => {
  if (currentSection < sectionNames.length) { // Check if there is a next section
    currentSection++;
    document.getElementById("success-container").style.display = "none";
    document.getElementById("quiz-container").style.display = "block";
    currentQuestion = 0;
    score = 0;
    document.getElementById("score").innerText = "0";
    loadQuizData();
  }
});
// Event listener for the "Skip" button
document.getElementById("skip-btn").addEventListener("click", () => {
  currentQuestion++;
  if (currentQuestion < quizData.length) {
    loadQuestion();
    const progress = (currentQuestion / quizData.length) * 100;
    document.getElementById("progress-bar-fill").style.width = `${progress}%`;
    document.getElementById("progress-bar-text").innerText = `${Math.round(progress)}%`;
  } else {
    endQuiz();
  }
});

document.addEventListener('DOMContentLoaded', function() {
  const closePathwayBtn = document.getElementById('close-pathway-btn');
  const pathwayContainer = document.getElementById('pathway-container');
  const failureContainer = document.getElementById('failure-container');

  if (closePathwayBtn) {
      closePathwayBtn.addEventListener('click', function() {
          pathwayContainer.style.display = 'none';
          failureContainer.style.display = 'block';
      });
  }
});