<!DOCTYPE html>
<html>
<head>
    <title>Seasons Image Placeholder</title>
    <style>
        .seasons-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            width: 400px;
            height: 300px;
            border: 2px solid #333;
            font-family: Arial, sans-serif;
        }
        .season {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 1px solid #666;
            font-size: 18px;
            font-weight: bold;
        }
        .spring { background: linear-gradient(135deg, #90EE90, #98FB98); color: #2E8B57; }
        .summer { background: linear-gradient(135deg, #FFD700, #FFA500); color: #FF4500; }
        .autumn { background: linear-gradient(135deg, #FF8C00, #CD853F); color: #8B4513; }
        .winter { background: linear-gradient(135deg, #87CEEB, #B0E0E6); color: #4682B4; }
        .emoji { font-size: 40px; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="seasons-container">
        <div class="season spring">
            <div class="emoji">🌸</div>
            <div>SPRING</div>
        </div>
        <div class="season summer">
            <div class="emoji">☀️</div>
            <div>SUMMER</div>
        </div>
        <div class="season autumn">
            <div class="emoji">🍂</div>
            <div>AUTUMN</div>
        </div>
        <div class="season winter">
            <div class="emoji">❄️</div>
            <div>WINTER</div>
        </div>
    </div>
</body>
</html>

<!-- 
Note: This is a placeholder for the seasons.png image.
To create the actual image:
1. Open this HTML file in a browser
2. Take a screenshot of the seasons grid
3. Save as seasons.png in the public folder
4. The image should show four quadrants with Spring (flowers), Summer (sun), Autumn (leaves), and Winter (snowflake)
-->
